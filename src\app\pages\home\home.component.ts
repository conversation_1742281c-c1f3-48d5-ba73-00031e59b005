import { Component, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/auth.service';
import { BookingService } from '@core/services/booking.service';
import { ServicesService } from '@core/services/services.service';
import { SettingsService } from '@core/services/settings.service';
import {
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonIcon,
  IonModal,
  IonText,
  IonTitle,
  IonToolbar,
  NavController,
} from '@ionic/angular/standalone';
import { IService } from '@models/interfaces/booking';
import { AppbarComponent } from '@shared/appbar/appbar.component';
import { ServiceItemComponent } from '@shared/service-item/service-item.component';
import { addIcons } from 'ionicons';
import {
  chevronUpOutline,
  personCircle,
  personCircleOutline,
  personOutline,
  searchOutline,
} from 'ionicons/icons';
import { FooterComponent } from '../../shared/footer/footer.component';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  imports: [
    IonToolbar,
    IonText,
    IonContent,
    IonButton,
    IonIcon,
    AppbarComponent,
    ServiceItemComponent,
    FormsModule,
    IonModal,
    IonButton,
    FooterComponent,
    IonHeader,
    IonTitle,
    IonButtons,
  ],
})
export class HomeComponent {
  protected settingsService = inject(SettingsService);
  protected servicesService = inject(ServicesService);
  protected bookingService = inject(BookingService);
  protected authService = inject(AuthService);
  protected companyInfo = this.settingsService.companyInfo!;
  protected services = this.servicesService.services!;
  protected isLoading = signal<boolean>(false);
  protected selectedServices = this.servicesService.selectedServices;
  protected filteredServices = signal<IService[]>([]);
  protected userInfo = this.authService.user;
  isDateModalOpen = signal(false);

  constructor(private navCtrl: NavController) {
    trackEvent('home_page');
    addIcons({
      personCircleOutline,
      personCircle,
      personOutline,
      searchOutline,
      chevronUpOutline,
    });

    this.filteredServices.set(this.services());
    // Rimossa la logica di aggiornamento metadati
  }

  manageSelectedServices(service: IService) {
    this.servicesService.manageSelectedServices(service);
  }

  isServiceSelected(service: IService): boolean {
    return this.selectedServices().some((s) => s.id === service.id);
  }

  onProfileClick() {
    this.isLoading.set(true);
    this.navCtrl.navigateForward('user').then(() => {
      this.isLoading.set(false);
    });
  }

  onBookClick() {
    this.isLoading.set(true);
    this.bookingService.setServices(this.selectedServices());
    this.navCtrl.navigateForward('booking').then(() => {
      this.isLoading.set(false);
    });
  }

  onSearchChange(event: string) {
    this.filteredServices.set(
      this.services().filter((service) =>
        service.name.toLowerCase().includes(event.toLowerCase()),
      ),
    );
  }

  closeModal() {
    this.isDateModalOpen.set(false);
  }
}
