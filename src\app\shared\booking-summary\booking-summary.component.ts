import { <PERSON><PERSON><PERSON>cyPipe, DatePipe, NgTemplateOutlet } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  input,
  OnInit,
} from '@angular/core';
import {
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonItem,
  IonLabel,
} from '@ionic/angular/standalone';
import { IBooking } from '@models/interfaces/booking';
import { CupertinoPane } from 'cupertino-pane';
import { addIcons } from 'ionicons';
import {
  briefcaseOutline,
  calendarOutline,
  cashOutline,
  chevronDownOutline,
  chevronForwardOutline,
  mailOutline,
  personOutline,
  timeOutline,
} from 'ionicons/icons';

@Component({
  selector: 'app-booking-summary',
  templateUrl: './booking-summary.component.html',
  styleUrls: ['./booking-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    IonCard,
    IonCardContent,
    IonItem,
    IonLabel,
    IonIcon,
    DatePipe,
    CurrencyPipe,
    NgTemplateOutlet,
    IonCardHeader,
    IonCardTitle,
  ],
})
export class BookingSummaryComponent implements OnInit {
  booking = input.required<Partial<IBooking>>();
  drawer: CupertinoPane | null = null;
  showPane = input<boolean>(true);

  constructor() {
    addIcons({
      personOutline,
      mailOutline,
      calendarOutline,
      timeOutline,
      briefcaseOutline,
      cashOutline,
      chevronForwardOutline,
      chevronDownOutline,
    });
  }

  async ngOnInit() {
    if (this.showPane()) {
      setTimeout(async () => {
        await this.createPanel();
      }, 1);
    }
  }

  async createPanel() {
    this.drawer = new CupertinoPane('.cupertino-pane', {
      inverse: true,
      backdrop: false,
      breaks: {
        bottom: { enabled: true, height: 30, bounce: false },
        middle: { enabled: true, height: 400, bounce: false },
        top: { enabled: true, height: 400, bounce: false },
      },
      initialBreak: 'bottom',
      events: {
        // onDidPresent: () => {
        //   console.log('onDidPresent');
        // },
        // onDidDismiss: () => {
        //   console.log('onDidDismiss');
        // },
        // onBackdropTap: () => this.setMiddleDrawer(),
        // onDragStart: () => this.showBackdrop(),
      },
    });
    await this.drawer.present({ animate: true });
  }

  // async setMiddleDrawer() {
  //   this.drawer?.moveToBreak('bottom');
  //   this.hideBackdrop();
  // };

  // showBackdrop() {
  //   this.drawer?.moveToBreak('top');
  //   const backdrop = document.getElementsByClassName('backdrop')[0];
  //   backdrop.setAttribute('style', 'transition: 300ms; background-color: rgba(0, 0, 0, 0.6); display: block;');
  // }

  // hideBackdrop() {
  //   const backdrop = document.getElementsByClassName('backdrop')[0];
  //   backdrop.setAttribute('style', 'z-index: -1;');
  // }

  ngOnDestroy() {
    this.drawer?.destroy({ animate: true });
  }
}
