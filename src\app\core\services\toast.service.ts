import { Injectable, inject } from '@angular/core';
import { ToastController } from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import {
  alertCircle,
  checkmarkCircle,
  closeCircle,
  informationCircle,
  warningOutline,
} from 'ionicons/icons';

/**
 * Interfaccia per le opzioni del toast
 */
export interface ToastOptions {
  message: string;
  color?:
    | 'primary'
    | 'secondary'
    | 'tertiary'
    | 'success'
    | 'warning'
    | 'danger'
    | 'light'
    | 'medium'
    | 'dark';
  duration?: number;
  position?: 'top' | 'middle' | 'bottom';
  icon?: string;
  showCloseButton?: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  private toastController = inject(ToastController);

  constructor() {
    // Registra le icone necessarie per i toast
    addIcons({
      alertCircle,
      checkmarkCircle,
      closeCircle,
      informationCircle,
      warningOutline,
    });
  }

  /**
   * Mostra un toast con le opzioni specificate
   * @param options Le opzioni del toast
   */
  async showToast(options: ToastOptions) {
    // Valori predefiniti
    const defaults: Partial<ToastOptions> = {
      color: 'primary',
      duration: 3000,
      showCloseButton: false,
      position: 'top',
    };

    // Unisci le opzioni con i valori predefiniti
    const config = { ...defaults, ...options };

    // Configura i pulsanti
    const buttons = config.showCloseButton
      ? [{ text: 'Chiudi', role: 'cancel' }]
      : undefined;

    const toast = await this.toastController.create({
      message: config.message,
      duration: config.duration,
      position: config.position as 'top' | 'middle' | 'bottom',
      color: config.color,
      icon: config.icon,
      buttons,
    });

    await toast.present();
  }

  /**
   * Mostra un toast di errore
   * @param message Il messaggio di errore da mostrare
   */
  async showError(message: string = 'Si è verificato un errore') {
    await this.showToast({
      message,
      color: 'danger',
      icon: 'warning-outline',
    });
  }

  /**
   * Mostra un toast di successo
   * @param message Il messaggio di successo da mostrare
   */
  async showSuccess(message: string) {
    await this.showToast({
      message,
      color: 'success',
      icon: 'checkmark-circle',
    });
  }

  /**
   * Mostra un toast di avviso
   * @param message Il messaggio di avviso da mostrare
   */
  async showWarning(message: string) {
    await this.showToast({
      message,
      color: 'warning',
      icon: 'warning-outline',
    });
  }

  /**
   * Mostra un toast informativo
   * @param message Il messaggio informativo da mostrare
   */
  async showInfo(message: string) {
    await this.showToast({
      message,
      color: 'primary',
      icon: 'information-circle',
    });
  }
}
