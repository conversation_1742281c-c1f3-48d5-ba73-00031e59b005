import { Component, inject, signal, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/auth.service';
import { BookingService } from '@core/services/booking.service';
import { ToastService } from '@core/services/toast.service';
import {
  IonButton,
  IonButtons,
  IonContent,
  IonHeader,
  IonInfiniteScroll,
  IonInfiniteScrollContent,
  IonItem,
  IonList,
  IonListHeader,
  IonModal,
  IonRefresher,
  IonRefresherContent,
  IonSpinner,
  IonTitle,
  IonToggle,
  IonToolbar,
  ModalController,
  NavController,
} from '@ionic/angular/standalone';
import { bookingStatusType, IBooking } from '@models/interfaces/booking';
import { requestFilterOperatorType } from '@models/interfaces/request';
import { AppbarComponent } from '@shared/appbar/appbar.component';
import { addIcons } from 'ionicons';
import {
  optionsOutline,
  personCircleOutline,
  sunny,
  sunnyOutline,
} from 'ionicons/icons';
import { switchMap } from 'rxjs';
import { BookingCardComponent } from '../../shared/booking-card/booking-card.component';
import { BookingEditComponent } from '../../shared/booking-edit/booking-edit.component';

@Component({
  selector: 'app-user',
  templateUrl: './user.component.html',
  styleUrls: ['./user.component.scss'],
  imports: [
    IonHeader,
    IonToolbar,
    IonButtons,
    IonTitle,
    IonContent,
    IonList,
    IonListHeader,
    IonItem,
    IonToggle,
    IonSpinner,
    BookingCardComponent,
    IonRefresher,
    IonRefresherContent,
    IonModal,
    IonButton,
    FormsModule,
    AppbarComponent,
    IonInfiniteScroll,
    IonInfiniteScrollContent,
  ],
})
export class UserComponent {
  paletteToggle = signal<boolean>(false);
  isModalOpen = signal(false);
  protected authService = inject(AuthService);
  protected bookingService = inject(BookingService);
  protected modalCtrl = inject(ModalController);

  protected user = this.authService.user;
  protected bookingStatusType = bookingStatusType;

  protected bookings = signal<IBooking[] | undefined>(undefined);
  protected isLoading = signal<boolean>(true);
  protected userId = this.authService.user()?.id;
  protected totalPages!: number;
  protected totalRecords = signal<number | undefined>(undefined);
  protected PAGE_SIZE = 10 as const;
  protected pageIndex: number = 1;
  protected hasMoreData = signal<boolean>(true);
  protected userService = inject(AuthService);

  @ViewChild(IonModal) modal!: IonModal;
  @ViewChild(IonInfiniteScroll) infiniteScroll!: IonInfiniteScroll;

  constructor(
    private navCtrl: NavController,
    private toastService: ToastService,
  ) {
    trackEvent('user_page');
    addIcons({
      personCircleOutline,
      sunny,
      sunnyOutline,
      optionsOutline,
    });
  }

  async onEditBooking(booking: IBooking) {
    const modal = await this.modalCtrl.create({
      component: BookingEditComponent,
      componentProps: {
        bookingData: booking, // Utilizziamo la nuova proprietà bookingData invece di booking
      },
      cssClass: 'booking-edit-modal',
    });

    await modal.present();

    const { data, role } = await modal.onWillDismiss();

    if (role === 'confirm' && (data as IBooking)) {
      this.isLoading.set(true);
      // Aggiorna la prenotazione nella lista
      this.bookingService
        .updatedBooking(data, data.id!)
        .pipe(switchMap(() => this.userService.getUserProfile(this.userId!)))
        .subscribe({
          next: () => {
            this.bookings.update(
              (bookings) =>
                bookings?.map((b) => (b.id === data.id ? data : b)) || [],
            );
            this.toastService.showSuccess(
              'Prenotazione aggiornata con successo!',
            );
            this.isLoading.set(false);
          },
          error: () => {
            this.toastService.showError('Si è verificato un errore');
            this.isLoading.set(false);
          },
        });
    }
  }

  handleRefresh(event: CustomEvent) {
    // Resetta la paginazione e ricarica i dati
    this.pageIndex = 1;
    this.hasMoreData.set(true);
    if (this.infiniteScroll) {
      this.infiniteScroll.disabled = false;
    }
    this.getBookings(this.pageIndex);

    // Completa l'evento di refresh
    setTimeout(() => {
      (event.target as HTMLIonRefresherElement).complete();
    }, 1000);
  }

  ionViewWillEnter() {
    this.getBookings(this.pageIndex);
    // Use matchMedia to check the user preference
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
    const isDark =
      document.documentElement.classList.contains('ion-palette-dark');
    this.paletteToggle.set(isDark);

    // Listen for changes to the prefers-color-scheme media query
    prefersDark.addEventListener('change', (mediaQuery) =>
      this.initializeDarkPalette(mediaQuery.matches),
    );
  }

  // Check/uncheck the toggle and update the palette based on isDark
  initializeDarkPalette(isDark: boolean) {
    this.paletteToggle.set(isDark);
    this.toggleDarkPalette(isDark);
  }

  onProfileClick() {
    this.isLoading.set(true);
    this.navCtrl.navigateForward('/user/profile').then(() => {
      this.isLoading.set(false);
    });
  }

  onPastBookingsClick() {
    this.isLoading.set(true);
    this.navCtrl.navigateForward('/user/past-bookings').then(() => {
      this.isLoading.set(false);
    });
  }

  // Listen for the toggle check/uncheck to toggle the dark palette
  toggleChange(event: CustomEvent) {
    this.toggleDarkPalette(event.detail.checked);
    this.paletteToggle.set(event.detail.checked);
  }

  // Add or remove the "ion-palette-dark" class on the html element
  toggleDarkPalette(shouldAdd: boolean) {
    document.documentElement.classList.toggle('ion-palette-dark', shouldAdd);
  }

  cancel() {
    this.modal.dismiss(null, 'cancel');
  }

  loadMoreData(event: any) {
    if (this.pageIndex < this.totalPages) {
      this.pageIndex++;
      this.getBookings(this.pageIndex, event);
    } else {
      this.hasMoreData.set(false);
      event.target.complete();
      event.target.disabled = true;
    }
  }

  getBookings(pageIndex: number, event?: any) {
    // Imposta isLoading a true solo per il caricamento iniziale, non per l'infinite scroll
    if (pageIndex === 1 && !event) {
      this.isLoading.set(true);
    }

    const requestFilter = [
      {
        operator: requestFilterOperatorType.and,
        items: [
          {
            name: 'status',
            operator: requestFilterOperatorType.eq,
            type: 'string',
            value: bookingStatusType.booked,
          },
          {
            name: 'status',
            operator: requestFilterOperatorType.eq,
            type: 'string',
            value: bookingStatusType.pending,
          },
        ],
      },
    ];
    this.authService
      .searchUserBookings(
        this.userId!,
        {
          page: {
            size: this.PAGE_SIZE,
            index: pageIndex,
          },
        },
        requestFilter,
      )
      .subscribe({
        next: (res) => {
          const _bookings = res.data;
          // Se è la prima pagina, imposta i dati, altrimenti aggiungi ai dati esistenti
          if (pageIndex === 1) {
            this.bookings.set(_bookings ?? []);
          } else {
            this.bookings.update((currentBookings) => [
              ...(currentBookings ?? []),
              ...(_bookings ?? []),
            ]);
          }

          this.totalPages = res.meta?.page.totalPages!;
          this.totalRecords.set(res.meta?.page.total!);

          // Imposta isLoading a false solo per il caricamento iniziale
          if (pageIndex === 1 && !event) {
            this.isLoading.set(false);
          }

          // Se c'è un evento di infinite scroll, completalo
          if (event) {
            event.target.complete();

            // Disabilita l'infinite scroll se non ci sono più dati
            if (pageIndex >= this.totalPages) {
              this.hasMoreData.set(false);
              event.target.disabled = true;
            }
          }
        },
        error: () => {
          if (pageIndex === 1 && !event) {
            this.isLoading.set(false);
          }
          if (event) {
            event.target.complete();
          }
        },
      });
  }

  setOpen(isOpen: boolean) {
    this.isModalOpen.set(isOpen);
  }

  onDeleteBooking(booking: IBooking) {
    this.isLoading.set(true);
    this.bookingService
      .deleteBooking(booking.id!)
      .pipe(switchMap(() => this.userService.getUserProfile(this.userId!)))
      .subscribe({
        next: () => {
          // Resetta la paginazione e ricarica i dati
          this.pageIndex = 1;
          this.hasMoreData.set(true);
          if (this.infiniteScroll) {
            this.infiniteScroll.disabled = false;
          }
          this.getBookings(this.pageIndex);
          this.isLoading.set(false);
        },
        error: () => this.isLoading.set(false),
      });
  }

  goToHome() {
    this.navCtrl.navigateRoot('/');
  }
}
