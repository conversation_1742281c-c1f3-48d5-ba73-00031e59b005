import { HttpClient } from '@angular/common/http';
import { computed, Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
import { IBaseResponse } from '@models/interfaces/base-response';
import { ICompany } from '@models/interfaces/company';
import { tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SettingsService {
  private _baseSettingsApi = environment.api.settings;
  private _companyInfo = signal<ICompany | undefined>(undefined);
  public companyInfo = this._companyInfo.asReadonly();

  public companyAddress = computed(() => {
    this.companyInfo()?.geolocation?.street;

    return `${this.companyInfo()?.geolocation?.street}, ${this.companyInfo()?.geolocation?.streetNumber} - ${this.companyInfo()?.geolocation?.postalCode} ${this.companyInfo()?.geolocation?.city} (${this.companyInfo()?.geolocation?.province})`;
  });

  public shortCompanyAddress = computed(() => {
    return `${this.companyInfo()?.geolocation?.street}, ${this.companyInfo()?.geolocation?.streetNumber} -  ${this.companyInfo()?.geolocation?.city} (${this.companyInfo()?.geolocation?.province})`;
  });

  constructor(private http: HttpClient) {}

  getCompanyInfo() {
    return this.http
      .get<IBaseResponse<ICompany>>(`${this._baseSettingsApi}/company`)
      .pipe(tap(res => this._companyInfo.set(res.data!)));
  }

  getTenantId(url: string) {
    return this.http
      .get<IBaseResponse<string>>(`${this._baseSettingsApi}/tenant?url=${url}`)
      .pipe(tap(res => localStorage.setItem('x-tenant-id', res.data!)));
  }
}
