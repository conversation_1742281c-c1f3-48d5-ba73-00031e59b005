.main-app {
  // width: 100dvw;
  // height: 100dvh;

  @media (min-width: 768px) {
    max-width: 768px;
    display: flex;
    margin: auto;
    justify-content: center;
    align-items: center;
  }

  @media (min-width: 1024px) {
    max-width: 1024px;
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.initalize-app {
  position: fixed;
  width: 100dvw;
  height: 100dvh;
  top: 0;
  left: 0;
  z-index: 9999;
  display: grid;
  place-content: center;
  overflow: hidden;

  img {
    transform: scale(1);
    // animation: pulse 1s infinite;
    transition: opacity 0.2s ease-in;
  }

  &.hidden {
    display: hidden;
    animation: 1s ease-in-out;
    transition: opacity 0.5s ease-in;
    opacity: 0;

    img {
      &.hidden {
        transform: scale(1);
        animation: pulse-final 0.5s;
        opacity: 0;
      }
    }
  }
}

@keyframes pulse-final {
  0% {
    transform: scale(1);
  }

  70% {
    transform: scale(4.5);
  }

  100% {
    transform: scale(9);
  }
}
