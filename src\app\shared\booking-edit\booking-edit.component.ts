import {
  Component,
  inject,
  OnInit,
  output,
  signal,
  ViewChild,
} from '@angular/core';
import { trackEvent } from '@aptabase/web';
import { StaffService } from '@core/services/staff.service';
import {
  IonButton,
  IonContent,
  IonDatetime,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonModal,
  IonSpinner,
  IonTitle,
  IonToolbar,
  ModalController,
} from '@ionic/angular/standalone';
import { IBooking } from '@models/interfaces/booking';
import { BookingSummaryComponent } from '@shared/booking-summary/booking-summary.component';
import { add, format } from 'date-fns';
import { it } from 'date-fns/locale';
import { addIcons } from 'ionicons';
import {
  calendarOutline,
  checkmarkCircle,
  chevronDown,
  personOutline,
  timeOutline,
} from 'ionicons/icons';

@Component({
  selector: 'app-booking-edit',
  templateUrl: './booking-edit.component.html',
  styleUrls: ['./booking-edit.component.scss'],
  standalone: true,
  imports: [
    IonContent,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButton,
    IonItem,
    IonLabel,
    IonList,
    IonIcon,
    IonModal,
    IonDatetime,
    IonSpinner,
    BookingSummaryComponent,
  ],
})
export class BookingEditComponent implements OnInit {
  @ViewChild('dateModal') dateModal!: IonModal;

  booking = signal<IBooking | null>(null);

  // Proprietà per ricevere il valore dal modale
  private _bookingData?: IBooking;

  onClose = output<void>();
  onUpdate = output<IBooking>();

  protected isLoading = signal<boolean>(true);
  protected isDateModalOpen = signal<boolean>(false);

  protected selectedDate = signal<string | null>(null);
  protected formattedDate = signal<string>('');
  protected selectedTime = signal<string | null>(null);

  protected availableTimes = signal<string[]>([]);
  protected disabledDates = signal<string[]>([]);
  protected minDate = signal<string>(format(new Date(), 'yyyy-MM-dd'));
  protected maxDate = signal<string>(
    format(add(new Date(), { years: 1 }), 'yyyy-MM-dd'),
  );

  private staffService = inject(StaffService);
  private modalCtrl = inject(ModalController);

  // Setter per impostare il valore del booking da componentProps
  set bookingData(value: IBooking) {
    this._bookingData = value;
    // Imposta il valore del signal booking
    if (value) {
      // Utilizziamo un setTimeout per assicurarci che il signal sia pronto
      setTimeout(() => {
        this.booking.set(value);
      });
    }
  }

  constructor() {
    trackEvent('booking_edit_page');
    addIcons({
      calendarOutline,
      chevronDown,
      timeOutline,
      personOutline,
      checkmarkCircle,
    });
  }

  ngOnInit() {
    // Se abbiamo ricevuto i dati tramite componentProps, impostiamo il signal
    if (this._bookingData) {
      this.booking.set(this._bookingData);
    }

    // Carica le date disabilitate e gli slot disponibili
    this.fetchDisabledDates();
  }

  /**
   * Carica le date disabilitate per lo staff selezionato
   */
  fetchDisabledDates() {
    const today = new Date();
    // Utilizziamo il valore del signal booking se disponibile, altrimenti usiamo _bookingData
    const booking = this.booking();
    if (!booking) {
      console.error('Nessuna prenotazione disponibile');
      this.isLoading.set(false);
      return;
    }

    const staffId = booking.staff.id;

    this.staffService
      .getCalendarDisabledDates(
        format(today, 'yyyy-MM-dd'),
        format(add(today, { years: 1 }), 'yyyy-MM-dd'),
        staffId,
      )
      .subscribe({
        next: (res) => {
          this.disabledDates.set(res.data!);
          // Dopo aver impostato le date disabilitate, impostiamo la data iniziale
          this.setInitialDate();
          // Dopo aver impostato la data iniziale, carichiamo gli slot disponibili
          if (this.selectedDate()) {
            this.loadAvailableSlots();
          }
        },
        error: (err) => {
          console.error('Errore nel caricamento delle date disabilitate:', err);
          this.isLoading.set(false);
        },
      });
  }

  /**
   * Imposta la prima data disponibile (non disabilitata) come data selezionata
   * Se la data odierna è disponibile, viene selezionata quella
   * Altrimenti viene selezionata la prima data disponibile successiva
   */
  setInitialDate() {
    const today = new Date();
    const disabledDates = this.disabledDates();

    // Verifica se la data odierna è disponibile
    const todayFormatted = format(today, 'yyyy-MM-dd');
    if (!disabledDates.includes(todayFormatted)) {
      // La data odierna è disponibile, la impostiamo
      this.selectedDate.set(todayFormatted);
      this.formattedDate.set(format(today, 'dd/MM/yyyy', { locale: it }));
      return;
    }

    // La data odierna non è disponibile, cerchiamo la prima data disponibile
    let currentDate = today;
    let found = false;

    // Cerchiamo per i prossimi 365 giorni (un anno)
    for (let i = 1; i <= 365 && !found; i++) {
      currentDate = add(today, { days: i });
      const dateFormatted = format(currentDate, 'yyyy-MM-dd');

      if (!disabledDates.includes(dateFormatted)) {
        // Abbiamo trovato una data disponibile
        this.selectedDate.set(dateFormatted);
        this.formattedDate.set(
          format(currentDate, 'dd/MM/yyyy', { locale: it }),
        );
        found = true;
      }
    }

    // Se non abbiamo trovato date disponibili, manteniamo la data odierna
    if (!found) {
      this.selectedDate.set(todayFormatted);
      this.formattedDate.set(format(today, 'dd/MM/yyyy', { locale: it }));
    }
  }

  /**
   * Carica gli slot disponibili per lo staff e la data selezionati
   */
  loadAvailableSlots() {
    const staffId = this.booking()!.staff.id;
    const date = this.selectedDate();

    if (staffId && date) {
      this.isLoading.set(true);
      this.staffService
        .getBookingAvailableSlots(
          date,
          this.booking()!.services.map((s) => s.id),
          staffId,
        )
        .subscribe({
          next: (res) => {
            this.availableTimes.set(res.data!);
            this.isLoading.set(false);
          },
          error: (err) => {
            console.error(
              'Errore nel caricamento degli slot disponibili:',
              err,
            );
            this.isLoading.set(false);
          },
        });
    }
  }

  /**
   * Gestisce il cambio di data
   */
  onDateChange(event: CustomEvent) {
    const selectedDate = event.detail.value;
    this.selectedDate.set(selectedDate);
    this.formattedDate.set(
      format(new Date(selectedDate), 'dd/MM/yyyy', { locale: it }),
    );
    this.isDateModalOpen.set(false);
    this.loadAvailableSlots();
  }

  /**
   * Gestisce la selezione dell'orario
   */
  onTimeSelect(time: string) {
    this.selectedTime.set(time);
  }

  /**
   * Verifica se una data è abilitata
   */
  isDateEnabled = (dateString: string) => {
    const formattedDate = format(new Date(dateString), 'yyyy-MM-dd');
    const disabledDates = this.disabledDates();

    // Verifica se la data è nella lista delle date disabilitate
    return !disabledDates.includes(formattedDate);
  };

  /**
   * Chiude la modale
   */
  dismiss() {
    this.modalCtrl.dismiss(null, 'cancel');
  }

  confirm(booking: IBooking) {
    this.modalCtrl.dismiss(booking, 'confirm');
  }

  /**
   * Aggiorna la prenotazione
   */
  updateBooking() {
    if (!this.selectedDate() || !this.selectedTime()) {
      return;
    }

    const _finalBooking = {
      ...this.booking()!,
      date: this.selectedDate()!,
      startTime: this.selectedTime()!,
    } as IBooking;

    this.confirm(_finalBooking);
  }
}
