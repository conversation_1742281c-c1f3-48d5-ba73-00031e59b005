{"name": "biuti-app", "version": "1.0.0", "author": "MEMMCode", "homepage": "https://biuti.memmcode.ddns.net/", "scripts": {"ng": "ng", "start": "ng serve", "start:dev": "NODE_ENV=dev ng serve --configuration dev", "start:local": "NODE_ENV=local ng serve --configuration local", "build:prod": "ng build --configuration prod && npm run post-build", "post-build": "gulp", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "format": "npx prettier --write .", "lint": "ng lint"}, "dependencies": {"@angular/animations": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@aptabase/web": "^0.4.3", "@ionic/angular": "^8.0.0", "@memmcode/becorekit": "^0.26.1", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@stripe/stripe-js": "^5.10.0", "cupertino-pane": "^1.4.22", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "dotenv-webpack": "^8.1.0", "ionicons": "^8.0.0", "lodash": "^4.17.21", "ngx-stripe": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "uuid": "^11.1.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-builders/custom-webpack": "19.0.1", "@angular-devkit/build-angular": "^19.0.0", "@angular-eslint/builder": "^19.0.0", "@angular-eslint/eslint-plugin": "^19.0.0", "@angular-eslint/eslint-plugin-template": "^19.0.0", "@angular-eslint/schematics": "^19.0.0", "@angular-eslint/template-parser": "^19.0.0", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@angular/language-service": "^19.0.0", "@ionic/angular-toolkit": "^12.1.1", "@types/jasmine": "~5.1.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.14", "@types/node": "^22.14.0", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "eslint": "^9.16.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "gulp": "^4.0.2", "gulp-clean": "^0.4.0", "gulp-purgecss": "^5.0.0", "gulp-uglify": "^3.0.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "^3.3.3", "sass": "^1.86.3", "typescript": "~5.6.3", "webpack": "^5.98.0"}, "description": "biuti-app"}