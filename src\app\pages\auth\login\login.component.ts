import { Component, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/auth.service';
import { CustomValidators } from '@core/validators/custom.validator';
import {} from '@ionic/angular';
import {
  IonButton,
  IonContent,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonList,
  IonText,
  IonTitle,
  IonToolbar,
  NavController,
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { mail } from 'ionicons/icons';
import { AppbarComponent } from '../../../shared/appbar/appbar.component';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  imports: [
    ReactiveFormsModule,
    FormsModule,
    IonButton,
    IonContent,
    IonText,
    IonList,
    IonItem,
    IonIcon,
    IonInput,
    AppbarComponent,
    IonHeader,
    IonTitle,
    IonToolbar,
  ],
})
export class LoginComponent {
  protected baseForm!: FormGroup;
  protected isLoading = signal<boolean>(false);

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private navCtrl: NavController,
  ) {
    trackEvent('login_page');
    this.initForm();
    addIcons({
      mail,
    });
  }

  private initForm() {
    this.baseForm = this.fb.group({
      email: [
        '',
        [Validators.required, Validators.pattern(CustomValidators.emailRegex)],
      ],
    });
  }

  onSubmitClick() {
    this.isLoading.set(true);
    this.authService.login(this.baseForm.value.email).subscribe({
      next: () => {
        this.navCtrl.navigateForward(['/auth/verify-code']);
        this.isLoading.set(false);
      },
      error: () => {
        this.isLoading.set(false);
      },
    });
  }

  onBackClick() {
    this.navCtrl.navigateBack(['']);
    this.authService.setUser(undefined);
  }

  onRegisterClick() {
    this.navCtrl.navigateForward(['auth/register']);
  }
}
