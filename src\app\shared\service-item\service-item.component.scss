.note {
  font-size: 14px;
  color: var(--strong-gray);

  :nth-child(2) {
    margin-left: 16px;
    color: #000;
    font-weight: 500;
  }
}

:host {
  display: block;
  margin-bottom: 12px;
}

ion-item.service-item {
  border-radius: 12px;
  --background: white;
  margin: 0 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
  --inner-padding-end: 16px;
  --inner-padding-start: 16px;
  --padding-start: 0;
  --padding-end: 0;
  --min-height: 60px;
  background-color: white;

  &.item-checkbox-checked {
    ion-label {
      color: white;
    }
    .note {
      :nth-child(2) {
        color: white;
      }
    }
  }

  ion-avatar {
    margin: 12px 0 12px 16px;
    width: 48px;
    height: 48px;
  }

  ion-checkbox {
    --size: 26px;
    margin-right: 8px;
  }

  ion-label {
    margin-left: 0;
    h2 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 6px;
    }
  }
}
