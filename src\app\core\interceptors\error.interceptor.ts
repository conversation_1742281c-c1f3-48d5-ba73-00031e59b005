import { HttpEvent, HttpHandlerFn, HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';
import { AuthService } from '@core/services/auth.service';
import { ToastService } from '@core/services/toast.service';
import { NavController } from '@ionic/angular';
import { errorType } from '@models/enums/errors';
import { IBaseErrorResponse } from '@models/interfaces/base-response';
import {
  BehaviorSubject,
  Observable,
  catchError,
  filter,
  finalize,
  switchMap,
  take,
} from 'rxjs';

export function errorInterceptor(
  req: HttpRequest<unknown>,
  next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> {
  const authService = inject(AuthService);
  const toastService = inject(ToastService);
  const navCtrl = inject(NavController);

  let refreshTokenInProgress: boolean = false;
  let refreshTokenSubject = new BehaviorSubject(false);

  const manageClientError = (code: number) => {
    switch (code) {
      case 401:
        // log('MANAGE CLIENT ERROR: ', code);
        authService.logout().subscribe();
        break;
      default:
        // log('MANAGE CLIENT ERROR: ', code);
        break;
    }
  };
  const manageServerError = (code: number) => {
    switch (code) {
      case 500:
        break;
      default:
        // log('MANAGE SERVER ERROR: ', code);
        break;
    }
  };

  const tryRefreshToken = () => {
    if (refreshTokenInProgress) {
      return refreshTokenSubject.pipe(
        filter((result) => !!result),
        take(1),
        switchMap(() => next(req)),
      );
    } else {
      refreshTokenInProgress = true;
      refreshTokenSubject.next(false);
      return authService.refreshToken().pipe(
        switchMap((token) => {
          refreshTokenSubject.next(true);
          return next(req);
        }),
        finalize(() => {
          refreshTokenInProgress = false;
        }),
      );
    }
  };

  return next(req).pipe(
    catchError((err) => {
      const error = err.error as IBaseErrorResponse;

      if (err.status === 449) {
        return tryRefreshToken();
      }
      if (err.status >= 400 && err.status < 500) {
        if (err.status !== 401) {
          // Mostra un toast di errore per gli errori client (tranne 401 che è gestito dal logout)
          toastService.showError(errorType[error.errorCode]);
        }
        manageClientError(err.status);
      }
      if (err.status >= 500 && err.status < 600) {
        // Mostra un toast di errore per gli errori server
        toastService.showError(
          'Si è verificato un errore nel server. Riprova più tardi.',
        );
        manageServerError(err.status);
      }
      throw err;
    }),
  );
}
