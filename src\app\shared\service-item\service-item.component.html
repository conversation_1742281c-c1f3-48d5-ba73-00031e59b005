<ion-item
  (click)="onClick(service())"
  [color]="isSelected() ? 'selected' : 'default'"
  lines="none"
  class="service-item"
>
  <ion-avatar slot="start" style="width: 35px">
    <img
      [alt]="service().name"
      [src]="service().imageUrl"
      class="img-contain"
    />
  </ion-avatar>

  <ion-checkbox [checked]="isSelected()">
    <ion-label>
      <h2>{{ service().name }}</h2>
      <ion-note class="note">
        <span>{{ service().duration }}min </span>
        <span>
          {{ service().price | currency: "EUR" }}
        </span>
      </ion-note>
    </ion-label>
  </ion-checkbox>
</ion-item>
