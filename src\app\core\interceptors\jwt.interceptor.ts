import { HttpEvent, HttpHandlerFn, HttpRequest } from '@angular/common/http';
import { GenericUtils } from '@core/utils/generic';
import { Observable } from 'rxjs';

export function jwtInterceptor(
  req: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> {
  if (localStorage.getItem(GenericUtils.session_token)) {
    const header = req.headers.append(
      'Authorization',
      localStorage.getItem(GenericUtils.session_token) || ''
    );
    req = req.clone({ headers: header, withCredentials: true });
  } else {
    req = req.clone({ withCredentials: true });
  }
  return next(req);
}
