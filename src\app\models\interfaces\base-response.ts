import { errorType } from '@models/enums/errors';

export interface IBaseResponse<T, D = any> {
  data?: T;
  error?: IBaseErrorResponse;
  meta?: D;
}

export interface IBaseErrorResponse {
  code: number;
  message: string;
  errorCode: keyof typeof errorType;
  status: number;
  statusCode: string;
}

export interface IFindResponseMeta {
  page: {
    total: number;
    pageIndex: number;
    pageSize: number;
    totalPages: number;
  };
}
