{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "cache": {"enabled": false}, "schematicCollections": ["@ionic/angular-toolkit"]}, "newProjectRoot": "projects", "projects": {"app": {"architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "configurations": {"dev": {"buildOptimizer": false, "extractLicenses": false, "namedChunks": true, "optimization": false, "sourceMap": true, "vendorChunk": false}, "local": {"buildOptimizer": false, "extractLicenses": false, "namedChunks": true, "optimization": false, "sourceMap": true, "vendorChunk": false}, "prod": {"aot": true, "budgets": [{"maximumError": "5mb", "maximumWarning": "500kb", "type": "initial"}, {"maximumError": "5mb", "maximumWarning": "2kb", "type": "anyComponentStyle"}], "extractLicenses": true, "namedChunks": false, "optimization": true, "outputHashing": "all", "sourceMap": false, "vendorChunk": true}, "test": {"extractLicenses": false, "optimization": true, "sourceMap": true}}, "defaultConfiguration": "prod", "options": {"allowedCommonJsDependencies": ["lodash"], "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}], "customWebpackConfig": {"path": "./webpack.config.js"}, "index": "src/index.html", "inlineStyleLanguage": "scss", "main": "src/main.ts", "outputPath": "public", "polyfills": "zone.js", "scripts": [], "styles": ["src/global.scss"], "tsConfig": "tsconfig.app.json"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "app:build"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "configurations": {"dev": {"buildTarget": "app:build:dev", "port": 4201, "proxyConfig": "src/environments/proxy.conf.js"}, "local": {"buildTarget": "app:build:local", "port": 4201, "proxyConfig": "src/environments/proxy.conf.js"}}, "defaultConfiguration": "dev"}, "test": {"builder": "@angular-devkit/build-angular:karma", "configurations": {"ci": {"progress": false, "watch": false}}, "options": {"assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "inlineStyleLanguage": "scss", "karmaConfig": "karma.conf.js", "main": "src/test.ts", "polyfills": "src/polyfills.ts", "scripts": [], "styles": ["src/global.scss", "src/theme/variables.scss", "src/theme/utils.scss"], "tsConfig": "tsconfig.spec.json"}}}, "prefix": "app", "projectType": "application", "root": "", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "sourceRoot": "src"}}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}}, "version": 1}