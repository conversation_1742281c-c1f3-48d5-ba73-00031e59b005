.booking-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
  background-color: #ffffff;
  margin-bottom: 16px;
}

/* Header con nome operatore e stato */
.card-top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px;
  border-bottom: 1px solid var(--ion-color-light-shade);
  background-color: rgba(var(--ion-color-primary-rgb), 0.03);
}

.staff-info {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 600;

  ion-icon {
    margin-right: 6px;
    margin-bottom: 1px;
    font-size: 14px;
    font-weight: 600;
  }
}

.status-badge {
  --background: #d1ffd8;
  --padding-start: 8px;
  --padding-end: 8px;
  --padding-top: 4px;
  --padding-bottom: 4px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;
  font-weight: 600;

  &.completed {
    --background: #d1ffd8;
    --color: #4caf50;
  }
  &.booked {
    --background: #e6f0ff;
    --color: var(--ion-color-primary);
  }
  &.pending {
    --background: #fff3d1;
    --color: #e67e22;
  }
}

/* Stili per l'accordion */
ion-accordion-group {
  margin: 0;
  padding: 0;
}

ion-accordion {
  margin: 0;
  padding: 0;
}

/* Header dell'accordion (sempre visibile) */
.accordion-header {
  display: flex;
  padding: 24px 18px;
  margin: 0;
  background-color: #ffffff;
  position: relative;
  align-items: center;
}

/* Box data a sinistra */
.date-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #e6f0ff;
  padding: 12px;
  min-width: 90px;
  text-align: center;
  border-radius: 12px;

  .day-week {
    font-size: 12px;
    margin-bottom: 2px;
    color: var(--ion-color-primary);
  }

  .day {
    font-size: 28px;
    font-weight: 800;
    color: var(--ion-color-primary);
    line-height: 1;
    margin-bottom: 2px;
  }

  .month {
    font-size: 14px;
    font-weight: 600;
    color: var(--ion-color-primary);
    text-transform: uppercase;
  }
}

/* Contenuto a destra dell'header */
.header-content {
  flex: 1;
  padding: 4px 0 12px 16px;
  display: flex;
  flex-direction: column;
}

.time-row {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  font-size: 16px;

  .time-label {
    font-weight: 800;
    color: var(--ion-color-dark);
  }
}

.chevron-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;

  .expand-icon {
    font-size: 20px;
    color: var(--ion-color-medium);
  }
}

/* Servizi (anteprima nell'header) */
.services-preview {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .service-item {
    display: flex;
    align-items: center;

    span {
      font-size: 14px;
      color: var(--ion-color-medium);
    }
  }
}

/* Contenuto dell'accordion (visibile solo quando espanso) */
.accordion-content {
  padding: 0;
  border-top: 1px solid var(--ion-color-light-shade);
}

/* Dettagli completi */
.details-section {
  padding: 16px;
  background-color: #ffffff;
}

.detail-item {
  display: flex;

  &:not(:last-of-type) {
    margin-bottom: 20px;
  }

  .detail-icon {
    // background-color: #f0f0f0;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    justify-content: center;

    ion-icon {
      font-size: 18px;
      color: var(--ion-color-primary);
    }
  }

  .detail-content {
    flex: 1;

    .detail-label {
      font-size: 12px;
      color: var(--ion-color-medium);
      margin-bottom: 4px;
      font-weight: 500;
    }

    .detail-value {
      font-size: 14px;
      font-weight: 600;
      color: var(--ion-color-dark);

      .service-item {
        margin-bottom: 2px;
      }
    }
  }
}

/* Durata e prezzo */
.duration-price-row {
  display: grid;
  grid-template-columns: 50% 50%;
  border-top: 1px solid var(--ion-color-light-shade);
  padding: 22px;
  font-size: 18px;

  ion-icon {
    color: var(--ion-color-primary);
    font-size: 18px;
  }

  .duration {
    display: grid;
    grid-template-columns: 20px auto;
    font-weight: 800;
  }

  .price {
    font-weight: 800;
    display: grid;
    justify-self: end;
    grid-template-columns: 20px auto;
    gap: 4px;
    margin-right: 1px;

    ion-icon.euro {
      font-size: 16px;
      margin-right: 6px;
      color: #000;
    }

    .value {
      display: flex;
      justify-content: end;
      color: var(--ion-color-dark);
    }
  }
  .subtitle {
    color: var(--ion-color-medium);
    font-size: 12px;
    font-weight: 500;
  }

  .value {
    grid-column: 1/-1;
    display: flex;
    align-items: center;
    color: var(--ion-color-dark);
  }
}

/* Pulsanti */
.action-buttons {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: rgba(var(--ion-color-primary-rgb), 0.03);
  gap: 8px;

  ion-button {
    width: 100%;
  }
}

.cancel-button,
.edit-button,
.pay-button {
  --padding-start: 6px;
  --padding-end: 6px;
  --padding-top: 6px;
  --padding-bottom: 6px;
  font-size: 12px;
  font-weight: 500;
  margin: 0;
  height: auto;
  width: 100px;

  ion-icon {
    font-size: 16px;
    margin-right: 6px;
  }
}

.cancel-button {
  --color: var(--ion-color-danger);
  --background: rgba(var(--ion-color-danger-rgb), 0.05);
  --background-activated: rgba(
    0,
    0,
    0,
    0.1
  ); /* Sfondo più scuro quando premuto */
  --background-activated-opacity: 1; /* Opacità piena per l'effetto */
  --background-hover: rgba(
    var(--ion-color-danger-rgb),
    0.1
  ); /* Mantiene lo stesso colore per hover */
  --border-radius: 8px;
  --box-shadow: none;
  margin: 0;
}

.edit-button {
  --color: var(--ion-color-primary);
  --background: rgba(var(--ion-color-primary-rgb), 0.05);
  --background-activated: rgba(
    0,
    0,
    0,
    0.1
  ); /* Sfondo più scuro quando premuto */
  --background-activated-opacity: 1; /* Opacità piena per l'effetto */
  --background-hover: rgba(
    var(--ion-color-primary-rgb),
    0.1
  ); /* Mantiene lo stesso colore per hover */
  --border-radius: 8px;
  --box-shadow: none;
  margin: 0;
}

.pay-button {
  --color: #fff;
  --background: var(--stripe-color);
  --background-activated: rgba(
    0,
    0,
    0,
    0.1
  ); /* Sfondo più scuro quando premuto */
  --background-activated-opacity: 1; /* Opacità piena per l'effetto */
  --background-hover: rgba(
    var(--stripe-color-rgb),
    0.1
  ); /* Mantiene lo stesso colore per hover */
  --border-radius: 8px;
  --box-shadow: none;
  margin: 0;
}

/* Stili per la modale di conferma */
.confirm-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 16px;
}

.warning-icon {
  font-size: 64px;
  color: var(--ion-color-danger);
  margin-bottom: 16px;
}

.confirm-buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 24px;
  gap: 16px;
}

.confirm-content h2 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.confirm-content p {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
}

.alert-button-cancel .alert-wrapper {
  --color: var(--ion-color-primary);
}

.alert-button-confirm .alert-wrapper {
  --color: var(--ion-color-danger);
}
