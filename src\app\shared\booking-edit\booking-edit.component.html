<ion-header>
  <ion-toolbar>
    <ion-title>Modifica Appuntamento</ion-title>
    <ion-button class="close-button" slot="end" fill="clear" (click)="dismiss()"
      ><PERSON>udi</ion-button
    >
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
  <!-- Riepilogo prenotazione -->
  <div class="summary-container">
    <app-booking-summary
      [booking]="booking()!"
      [showPane]="false"
    ></app-booking-summary>
  </div>

  <!-- Selezione data -->
  <h2 class="section-title">Seleziona la data</h2>
  <ion-list class="input-list">
    <ion-item
      lines="none"
      class="custom-item"
      (click)="isLoading() ? null : isDateModalOpen.set(true)"
      button
    >
      <ion-icon
        slot="start"
        name="calendar-outline"
        aria-label="Seleziona data"
        aria-hidden="false"
        tabindex="0"
      ></ion-icon>
      <ion-label>{{ formattedDate() }}</ion-label>
      <ion-icon
        slot="end"
        name="chevron-down"
        size="small"
        style="margin-right: 0"
        aria-label="Espandi calendario"
        aria-hidden="false"
        tabindex="0"
      ></ion-icon>
    </ion-item>
  </ion-list>

  <!-- Modal per il calendario -->
  <ion-modal
    [initialBreakpoint]="0.7"
    [breakpoints]="[0.7, 1]"
    [backdropDismiss]="true"
    [backdropBreakpoint]="0.5"
    [isOpen]="isDateModalOpen()"
    class="calendar-modal"
  >
    <ng-template>
      <ion-content class="calendar-modal-content">
        <ion-datetime
          class="centered-calendar"
          presentation="date"
          [value]="selectedDate()"
          (ionChange)="onDateChange($event)"
          (ionCancel)="isDateModalOpen.set(false)"
          (ionClose)="isDateModalOpen.set(false)"
          locale="it-IT"
          showDefaultButtons="true"
          doneText="Conferma"
          cancelText="Annulla"
          [isDateEnabled]="isDateEnabled"
          [min]="minDate()"
          [max]="maxDate()"
        ></ion-datetime>
      </ion-content>
    </ng-template>
  </ion-modal>

  <!-- Selezione orario -->
  <h2 class="section-title">Seleziona l'orario</h2>
  <div class="time-slots">
    @if (isLoading()) {
      <div class="spinner-container">
        <ion-spinner name="dots" class="custom-spinner"></ion-spinner>
      </div>
    } @else {
      @for (time of availableTimes(); track time) {
        <ion-button
          class="time-slot"
          [class.selected]="selectedTime() === time"
          (click)="onTimeSelect(time)"
          fill="outline"
          [attr.aria-label]="'Seleziona orario ' + time"
          tabindex="0"
        >
          {{ time }}
        </ion-button>
      } @empty {
        <p class="empty-slot">Nessun orario disponibile</p>
      }
    }
  </div>

  <!-- Spazio extra per evitare che il contenuto venga nascosto dal pulsante fisso -->
  <div class="bottom-spacer"></div>

  <!-- Pulsante di aggiornamento -->
  <div class="fixed-bottom-button">
    <ion-button
      expand="block"
      class="update-button"
      (click)="updateBooking()"
      [disabled]="isLoading() || !selectedDate() || !selectedTime()"
      aria-label="Aggiorna appuntamento"
      tabindex="0"
    >
      Aggiorna Appuntamento
    </ion-button>
  </div>
</ion-content>
