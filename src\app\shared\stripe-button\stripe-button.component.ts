import {
  Component,
  EventEmitter,
  Input,
  Output,
  computed,
  signal,
} from '@angular/core';
import { IonButton } from '@ionic/angular/standalone';

@Component({
  selector: 'app-stripe-button',
  standalone: true,
  imports: [IonButton],
  templateUrl: './stripe-button.component.html',
  styleUrl: './stripe-button.component.scss',
})
export class StripeButtonComponent {
  @Input() set style(value: { [key: string]: any }) {
    this.styleSignal.set(value);
  }
  @Input() set variant(value: 'pay' | 'manage') {
    this.variantSignal.set(value);
  }
  @Input() set text(value: string) {
    this.textSignal.set(value);
  }

  @Output() onClick = new EventEmitter<void>();

  styleSignal = signal<{ [key: string]: any }>({});
  variantSignal = signal<'pay' | 'manage'>('pay');
  textSignal = signal<string>('Paga con Stripe');

  buttonStyles = computed(() => {
    const baseStyles = {
      'font-weight': '500',
      width: 'auto',
      'min-width': 'fit-content',
      '--padding-start': '16px',
      '--padding-end': '16px',
      '--padding-top': '12px',
      '--padding-bottom': '12px',
      '--border-radius': '8px',
      '--box-shadow': 'none',
      '--transition': 'all 0.3s ease',
      ...this.styleSignal(),
    };

    if (this.variantSignal() === 'pay') {
      return {
        ...baseStyles,
        '--background': '#635bff',
        '--background-hover': '#4f46f8',
        '--background-activated': '#3f37f5',
        '--color': 'white',
        '--color-hover': 'white',
        '--color-activated': 'white',
      };
    } else {
      return {
        ...baseStyles,
        '--background': 'transparent',
        '--background-hover': '#f8f9fa',
        '--background-activated': 'rgba(99, 91, 255, 0.1)',
        '--color': '#635bff',
        '--color-hover': '#4f46f8',
        '--color-activated': '#3f37f5',
        '--border-color': '#635bff',
        '--border-style': 'solid',
        '--border-width': '1px',
      };
    }
  });

  handleClick() {
    this.onClick.emit();
  }
}
