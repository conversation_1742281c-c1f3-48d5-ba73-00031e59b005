import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { IonButton } from '@ionic/angular/standalone';

@Component({
  selector: 'app-stripe-button',
  standalone: true,
  imports: [IonButton],
  templateUrl: './stripe-button.component.html',
  styleUrl: './stripe-button.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class StripeButtonComponent {
  @Input() style: { [key: string]: any } = {};
  @Input() variant: 'pay' | 'manage' = 'pay';
  @Input() text: string = 'Paga con Stripe';
  @Output() onClick = new EventEmitter<void>();

  handleClick() {
    this.onClick.emit();
  }
}
