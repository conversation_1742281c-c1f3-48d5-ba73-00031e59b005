import { Ng<PERSON>ty<PERSON> } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IonButton } from '@ionic/angular/standalone';

@Component({
  selector: 'app-stripe-button',
  standalone: true,
  imports: [IonButton, NgStyle],
  templateUrl: './stripe-button.component.html',
  styleUrl: './stripe-button.component.scss',
})
export class StripeButtonComponent {
  @Input() style: { [key: string]: any } = {};
  @Input() variant: 'pay' | 'manage' = 'pay';
  @Input() text: string = 'Paga con Stripe';
  @Output() onClick = new EventEmitter<void>();

  // CSS Custom Properties di Ionic
  get buttonStyles() {
    const baseStyles = {
      'font-weight': '500',
      width: 'auto',
      'min-width': 'fit-content',
      '--padding-start': '16px',
      '--padding-end': '16px',
      '--padding-top': '12px',
      '--padding-bottom': '12px',
      '--border-radius': '8px',
      '--box-shadow': 'none',
      '--transition': 'all 0.3s ease',
      ...this.style,
    };

    if (this.variant === 'pay') {
      return {
        ...baseStyles,
        '--background': '#635bff',
        '--background-hover': '#4f46f8',
        '--background-activated': '#3f37f5',
        '--color': 'white',
        '--color-hover': 'white',
        '--color-activated': 'white',
      };
    } else {
      return {
        ...baseStyles,
        '--background': 'transparent',
        '--background-hover': '#f8f9fa',
        '--background-activated': 'rgba(99, 91, 255, 0.1)',
        '--color': '#635bff',
        '--color-hover': '#4f46f8',
        '--color-activated': '#3f37f5',
        '--border-color': '#635bff',
        '--border-style': 'solid',
        '--border-width': '1px',
      };
    }
  }

  handleClick() {
    this.onClick.emit();
  }
}
