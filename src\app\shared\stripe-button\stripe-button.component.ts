import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IonButton } from '@ionic/angular/standalone';

@Component({
  selector: 'app-stripe-button',
  standalone: true,
  imports: [IonButton],
  templateUrl: './stripe-button.component.html',
  styleUrl: './stripe-button.component.scss',
})
export class StripeButtonComponent {
  @Input() style: { [key: string]: any } = {};
  @Input() variant: 'pay' | 'manage' = 'pay';
  @Input() text: string = 'Paga con Stripe';
  @Input() disabled: boolean = false;
  @Output() onClick = new EventEmitter<void>();

  handleClick() {
    this.onClick.emit();
  }
}
