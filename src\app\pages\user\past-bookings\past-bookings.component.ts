import { Component, inject, signal, ViewChild } from '@angular/core';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/auth.service';
import {
  IonButton,
  IonContent,
  IonInfiniteScroll,
  IonInfiniteScrollContent,
  IonRefresher,
  IonRefresherContent,
  IonSpinner,
  NavController,
} from '@ionic/angular/standalone';
import { bookingStatusType, IBooking } from '@models/interfaces/booking';
import { requestFilterOperatorType } from '@models/interfaces/request';
import { AppbarComponent } from '@shared/appbar/appbar.component';
import { BookingCardComponent } from '@shared/booking-card/booking-card.component';
@Component({
  selector: 'app-past-bookings',
  templateUrl: './past-bookings.component.html',
  styleUrls: ['./past-bookings.component.scss'],
  standalone: true,
  imports: [
    BookingCardComponent,
    IonContent,
    A<PERSON>bar<PERSON>omponent,
    IonInfiniteScroll,
    IonInfiniteScrollContent,
    IonRefresher,
    IonRefresherContent,
    IonButton,
    IonSpinner,
  ],
})
export class PastBookingsComponent {
  protected authService = inject(AuthService);

  @ViewChild(IonInfiniteScroll) infiniteScroll!: IonInfiniteScroll;

  protected isLoading = signal(true);
  protected bookings = signal<IBooking[]>([]);

  protected totalPages!: number;
  protected totalRecords = signal<number | undefined>(undefined);
  protected PAGE_SIZE = 10 as const;
  protected pageIndex: number = 1;
  protected userId = this.authService.user()?.id;
  protected hasMoreData = signal<boolean>(true);

  constructor(private navCtrl: NavController) {
    trackEvent('past_bookings_page');
  }

  ionViewWillEnter() {
    this.getBookings(this.pageIndex);
  }

  handleRefresh(event: CustomEvent) {
    // Resetta la paginazione e ricarica i dati
    this.pageIndex = 1;
    this.hasMoreData.set(true);
    if (this.infiniteScroll) {
      this.infiniteScroll.disabled = false;
    }
    this.getBookings(this.pageIndex);

    // Completa l'evento di refresh
    setTimeout(() => {
      (event.target as HTMLIonRefresherElement).complete();
    }, 1000);
  }

  loadMoreData(event: any) {
    if (this.pageIndex < this.totalPages) {
      this.pageIndex++;
      this.getBookings(this.pageIndex, event);
    } else {
      this.hasMoreData.set(false);
      event.target.complete();
      event.target.disabled = true;
    }
  }

  goToHome() {
    this.navCtrl.navigateForward('/');
  }

  getBookings(pageIndex: number, event?: any) {
    // Imposta isLoading a true solo per il caricamento iniziale, non per l'infinite scroll
    if (pageIndex === 1 && !event) {
      this.isLoading.set(true);
    }

    const requestFilter = [
      {
        operator: requestFilterOperatorType.or,
        items: [
          {
            name: 'status',
            operator: requestFilterOperatorType.eq,
            type: 'string',
            value: bookingStatusType.completed,
          },
        ],
      },
    ];
    this.authService
      .searchUserBookings(
        this.userId!,
        {
          page: {
            size: this.PAGE_SIZE,
            index: pageIndex,
          },
        },
        requestFilter,
      )
      .subscribe({
        next: (res) => {
          const _bookings = res.data;

          // Se è la prima pagina, imposta i dati, altrimenti aggiungi ai dati esistenti
          if (pageIndex === 1) {
            this.bookings.set(_bookings ?? []);
          } else {
            this.bookings.update((currentBookings) => [
              ...currentBookings,
              ...(_bookings ?? []),
            ]);
          }

          this.totalPages = res.meta?.page.totalPages!;
          this.totalRecords.set(res.meta?.page.total!);

          // Imposta isLoading a false solo per il caricamento iniziale
          if (pageIndex === 1 && !event) {
            this.isLoading.set(false);
          }

          // Se c'è un evento di infinite scroll, completalo
          if (event) {
            event.target.complete();

            // Disabilita l'infinite scroll se non ci sono più dati
            if (pageIndex >= this.totalPages) {
              this.hasMoreData.set(false);
              event.target.disabled = true;
            }
          }
        },
        error: () => {
          if (pageIndex === 1 && !event) {
            this.isLoading.set(false);
          }
          if (event) {
            event.target.complete();
          }
        },
      });
  }
}
