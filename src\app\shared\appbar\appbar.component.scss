ion-toolbar {
  --padding-top: 24px;
  --padding-bottom: 24px;
  --padding-start: 12px;
  --padding-end: 12px;
  --min-height: 60px;
  border: none;
}

.back-button-container {
  background: var(--ion-color-primary);
  border-radius: 100%;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  padding-left: 13.5px;
  ion-back-button {
    --icon-margin-start: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
  }
}

.home-header {
  .logo-container {
    display: flex;
    padding-left: 2px;
    overflow: hidden;
  }
  .logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .profile-icon {
    font-size: 38px;
    cursor: pointer;
    color: #000;
  }
}

.user-label {
  text-wrap: nowrap;
  text-overflow: ellipsis;
  overflow-x: hidden;
}

.user-label-container {
  border-radius: 100px;
  height: 40px;
  max-width: 50%;
}
