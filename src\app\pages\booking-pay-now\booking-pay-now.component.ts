import {
  Component,
  ElementRef,
  inject,
  input,
  OnInit,
  signal,
  viewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { BookingService } from '@core/services/booking.service';
import { PaymentsService } from '@core/services/payment.service';
import {
  IonContent,
  IonHeader,
  IonTitle,
  IonToolbar,
} from '@ionic/angular/standalone';
import { IPayment } from '@models/interfaces/payment';
import {
  StripeComponent,
  stripeMode,
} from '@pages/booking-create/step-2-user-details/stripe/stripe.component';
import { AppbarComponent } from '@shared/appbar/appbar.component';
import { StripeButtonComponent } from '@shared/stripe-button/stripe-button.component';

@Component({
  selector: 'app-booking-pay-now',
  imports: [
    AppbarComponent,
    IonContent,
    IonHeader,
    IonToolbar,
    IonTitle,
    StripeComponent,
    StripeButtonComponent,
  ],
  templateUrl: './booking-pay-now.component.html',
  styleUrls: ['./booking-pay-now.component.scss'],
})
export class BookingPayNowComponent implements OnInit {
  // Services
  private bookingService = inject(BookingService);
  private paymentsService = inject(PaymentsService);
  private router = inject(Router);

  // Receive paymentId from URL path
  readonly paymentId = input<string>();

  stripeMode = stripeMode;

  protected currentBooking = this.bookingService.currentBooking;

  protected isLoading = signal<boolean>(false);

  stripeElement = viewChild<ElementRef<HTMLElement>>('stripe');

  protected isStripeReady = signal<boolean>(false);
  protected onPayClick = signal<boolean>(false);

  constructor() {
    trackEvent('booking_pay_now_page');
  }

  ngOnInit() {
    console.log('paymentId', this.paymentId());
  }

  onStripePaySuccess(paymentIntentId: string) {
    this.paymentsService
      .confirmPayment(
        (this.currentBooking()?.payment as IPayment).id,
        paymentIntentId,
      )
      .subscribe({
        next: () => {
          this.router
            .navigate(['booking/success'])
            .then(() => this.bookingService.setIsLoading(false));
        },
      });
  }

  onStripePayFailed() {
    this.bookingService.setIsLoading(false);
    this.onPayClick.set(false);
  }
}
