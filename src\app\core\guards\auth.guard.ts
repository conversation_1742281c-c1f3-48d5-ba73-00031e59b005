import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { AuthService } from '@core/services/auth.service';
import { NavController } from '@ionic/angular/standalone';

export const authGuard: CanActivateFn = (route, state) => {
  const router = inject(NavController);
  const authService = inject(AuthService);

  return authService.isLoggedIn() ? true : router.navigateRoot(['/auth/login']);
};
