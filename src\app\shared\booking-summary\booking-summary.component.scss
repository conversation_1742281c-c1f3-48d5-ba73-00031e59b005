.content {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  height: 100%;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #000;
}

.summary-card {
  border-radius: 12px;
  box-shadow: none;
  margin: 0;
  overflow: hidden;
}

.card-header {
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.summary-item {
  --background: transparent;
  --padding-start: 0;
  --inner-padding-end: 0;
  --min-height: 36px;

  ion-icon {
    color: #666;
    margin-right: 12px;
    font-size: 20px;
  }

  ion-label {
    margin: 0;
    font-size: 16px;
    color: #333;
  }
}

.separator {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.08);
  margin: 12px 0;
}

.service-section,
.total-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.service-label,
.total-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.service-details,
.total-details {
  display: flex;
  align-items: center;
}

.service-duration,
.total-duration {
  font-size: 14px;
  color: #666;
  margin-right: 12px;
}

.service-price,
.total-price {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.total-section {
  margin-top: 4px;
}

.total-label {
  font-weight: 600;
}

.total-price {
  font-weight: 600;
}
