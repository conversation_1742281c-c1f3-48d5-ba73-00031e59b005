@if (showPane()) {
  <div class="cupertino-pane">
    <div class="content">
      <ng-container *ngTemplateOutlet="tplContent"></ng-container>
    </div>
  </div>
} @else {
  <ng-container *ngTemplateOutlet="tplContent"></ng-container>
}

<ng-template #tplContent>
  <div class="booking-summary">
    <ion-card class="summary-card">
      @if (showPane()) {
        <ion-card-header>
          <ion-card-title>Riepilogo</ion-card-title>
        </ion-card-header>
      }
      <ion-card-content>
        <!-- Dati cliente -->
        @if (booking().user) {
          <div class="card-header">
            <h3 class="card-title">Cliente</h3>
          </div>
          <ion-item lines="none" class="summary-item" aria-label="Nome cliente">
            <ion-icon
              name="person-outline"
              slot="start"
              aria-label="Cliente"
            ></ion-icon>
            <ion-label>
              {{ booking().user!.name + " " + booking().user!.surname }}
            </ion-label>
          </ion-item>

          <ion-item
            lines="none"
            class="summary-item"
            aria-label="Email cliente"
          >
            <ion-icon
              name="mail-outline"
              slot="start"
              aria-label="Email"
            ></ion-icon>
            <ion-label>
              {{ booking().user!.email }}
            </ion-label>
          </ion-item>
          <!-- Separatore -->
          <div class="separator"></div>
        }

        <!-- Data e ora -->
        @if (booking().date) {
          <ion-item
            lines="none"
            class="summary-item"
            aria-label="Data prenotazione"
          >
            <ion-icon
              name="calendar-outline"
              slot="start"
              aria-label="Data"
            ></ion-icon>
            <ion-label>
              {{ booking().date | date: "EEEE d MMMM yyyy" : "" : "it" }}
            </ion-label>
          </ion-item>

          <ion-item lines="none" class="summary-item">
            <ion-icon
              name="time-outline"
              slot="start"
              aria-label="Ora"
            ></ion-icon>
            <ion-label>
              {{ booking().startTime }}
            </ion-label>
          </ion-item>
        }

        @if (booking().staff) {
          <!-- Operatore -->
          <ion-item lines="none" class="summary-item" aria-label="Operatore">
            <ion-icon
              name="briefcase-outline"
              slot="start"
              aria-label="Operatore"
            ></ion-icon>
            <ion-label>
              {{ booking().staff?.name + " " + booking().staff?.surname }}
            </ion-label>
          </ion-item>
        }

        <!-- Separatore -->
        <div class="separator"></div>

        <!-- Servizi -->
        @if (booking().services && booking().services!.length > 0) {
          @for (service of booking().services; track service.id) {
            <div class="service-section">
              <ion-label class="service-label">{{ service.name }}</ion-label>
              <div class="service-details">
                <span class="service-duration">{{ service.duration }} min</span>
                <span class="service-price">{{
                  service.price | currency: "EUR"
                }}</span>
              </div>
            </div>
          }
        }

        <!-- Separatore -->
        <div class="separator"></div>

        <!-- Totale -->
        <div class="total-section">
          <ion-label class="total-label">Totale</ion-label>
          <div class="total-details">
            <span class="total-duration">
              {{ booking().totalDuration }} min
            </span>
            <span class="total-price">
              {{ booking().totalPrice | currency: "EUR" }}
            </span>
          </div>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ng-template>
