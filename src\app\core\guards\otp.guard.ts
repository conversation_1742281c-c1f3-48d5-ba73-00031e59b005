import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { AuthService } from '@core/services/auth.service';
import { NavController } from '@ionic/angular/standalone';

export const otpGuard: CanActivateFn = (route, state) => {
  const router = inject(NavController);
  const authService = inject(AuthService);

  const isLoggedIn = authService.isLoggedIn();

  return authService.user()?.email && !isLoggedIn
    ? true
    : router.navigateRoot(['profile']);
};
