/** Define the http request filter object */
export interface IRequestFilter {
  operator: requestFilterOperatorType;
  items: IRequestFilterItem[];
}

/** Define the filter item object */
export interface IRequestFilterItem {
  name: string;
  operator: requestFilterOperatorType;
  type: string;
  value: any;
}

/** (Enum) Type of http request filter operators */
export enum requestFilterOperatorType {
  and = 'AND',
  or = 'OR',
  lt = 'LT',
  gt = 'GT',
  le = 'LE',
  ge = 'GE',
  eq = 'EQ',
  notEq = 'NOT_EQ',
  ne = 'NE',
  mongoId = 'MONGO_ID',
  fullString = 'FULL_STRING',
  partString = 'PART_STRING',
  orNull = 'OR_NULL',
  in = 'IN',
  nin = 'NIN',
  notNull = 'NOT_NULL',
  isNull = 'IS_NULL'
}

/** Define the http request meta object */
export interface IRequestMeta {
  page?: {
    size: number;
    index: number;
  };
  sort?: {
    field: any;
    order: string | 'ascend' | 'descend' | null;
  };
}
