import { NgTemplateOutlet } from '@angular/common';
import {
  Component,
  computed,
  inject,
  input,
  output,
  signal,
} from '@angular/core';
import { Router } from '@angular/router';
import { BookingService } from '@core/services/booking.service';
import { PaymentsService } from '@core/services/payment.service';
import { ToastService } from '@core/services/toast.service';
import {
  AlertButton,
  IonAccordion,
  IonAccordionGroup,
  IonAlert,
  IonBadge,
  IonButton,
  IonCard,
  IonIcon,
} from '@ionic/angular/standalone';
import { bookingStatusType, IBooking } from '@models/interfaces/booking';
import {
  IPayment,
  paymentMethodType,
  paymentStatusType,
} from '@models/interfaces/payment';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { addIcons } from 'ionicons';
import {
  alertCircleOutline,
  briefcaseOutline,
  calendarOutline,
  cartOutline,
  checkmarkCircle,
  chevronDownOutline,
  chevronUpOutline,
  closeOutline,
  createOutline,
  logoEuro,
  person,
  personOutline,
  pricetagOutline,
  shieldCheckmarkOutline,
  timeOutline,
  timerOutline,
  trashOutline,
  walletOutline,
} from 'ionicons/icons';

@Component({
  selector: 'app-booking-card',
  templateUrl: './booking-card.component.html',
  styleUrls: ['./booking-card.component.scss'],
  standalone: true,
  imports: [
    IonCard,
    IonButton,
    IonBadge,
    IonIcon,
    IonAccordion,
    IonAccordionGroup,
    IonAlert,
    NgTemplateOutlet,
  ],
})
export class BookingCardComponent {
  private router = inject(Router);
  private bookingService = inject(BookingService);
  private paymentService = inject(PaymentsService);
  private toastService = inject(ToastService);
  readonly booking = input.required<IBooking>();
  readonly onEdit = output<void>();
  readonly onCancel = output<void>();

  bookingStatusType = bookingStatusType;
  isConfirmCashModalOpen = signal<boolean>(false);
  isConfirmOnlineUnpdaiedPaymentModalOpen = signal<boolean>(false);
  isConfirmOnlinePaiedPaymentModalOpen = signal<boolean>(false);
  isExpanded = signal<boolean>(false);

  paymentMethodType = paymentMethodType;
  paymentStatusType = paymentStatusType;

  bookingStatusPending = computed(() => {
    return this.booking().status === bookingStatusType.pending;
  });

  bookingStatusBooked = computed(() => {
    return this.booking().status === bookingStatusType.booked;
  });

  bookingStatusBookedAndCashPayment = computed(() => {
    return (
      this.bookingStatusBooked() &&
      (this.booking().payment as IPayment).methodType == paymentMethodType.cash
    );
  });

  bookingPaymentStatusPaid = computed(() => {
    return (
      (this.booking().payment as IPayment).status === paymentStatusType.paid
    );
  });

  bookingStatusBookedAndOnlinePayment = computed(() => {
    return (
      this.bookingStatusBooked() &&
      (this.booking().payment as IPayment).methodType != paymentMethodType.cash
    );
  });

  bookingStatusPendingAndOnlinePayment = computed(() => {
    return (
      this.bookingStatusPending() &&
      (this.booking().payment as IPayment).methodType != paymentMethodType.cash
    );
  });

  protected alertCashButtons: AlertButton[] = [
    {
      text: 'Annulla',
      role: 'cancel',
      cssClass: 'alert-button-cancel',
      handler: () => {
        this.closeCashConfirmModal();
      },
    },
    {
      text: 'Conferma',
      role: 'confirm',
      cssClass: 'alert-button-confirm',
      handler: () => {
        this.confirmCashCancel();
      },
    },
  ];

  protected alertOnlineUnpaiedPaymentButtons: AlertButton[] = [
    {
      text: 'Annulla',
      role: 'cancel',
      cssClass: 'alert-button-cancel',
      handler: () => {
        this.closeOnlineUnapaiedPaymentConfirmModal();
      },
    },
    {
      text: 'Conferma',
      role: 'confirm',
      cssClass: 'alert-button-confirm',
      handler: () => {
        this.confirmOnlineUnapaiedPaymentCancel();
      },
    },
  ];

  constructor() {
    addIcons({
      calendarOutline,
      timeOutline,
      personOutline,
      person,
      timerOutline,
      createOutline,
      alertCircleOutline,
      closeOutline,
      checkmarkCircle,
      chevronDownOutline,
      chevronUpOutline,
      trashOutline,
      logoEuro,
      pricetagOutline,
      cartOutline,
      walletOutline,
      shieldCheckmarkOutline,
      briefcaseOutline,
    });
  }

  /**
   * Gestisce il cambio di stato dell'accordion
   */
  accordionChanged(event: any) {
    // Controlla se l'accordion è aperto (se il valore include 'booking-details')
    const isOpen = event.detail && event.detail.value === 'booking-details';
    this.isExpanded.set(isOpen);
  }

  openCashConfirmModal() {
    this.isConfirmCashModalOpen.set(true);
  }

  closeCashConfirmModal() {
    this.isConfirmCashModalOpen.set(false);
  }

  confirmCashCancel() {
    this.closeCashConfirmModal();
    this.onCancel.emit();
  }

  openOnlineUnapaiedPaymentConfirmModal() {
    this.isConfirmOnlineUnpdaiedPaymentModalOpen.set(true);
  }

  closeOnlineUnapaiedPaymentConfirmModal() {
    this.isConfirmOnlineUnpdaiedPaymentModalOpen.set(false);
  }

  openOnlinePaiedPaymentConfirmModal() {
    this.isConfirmOnlinePaiedPaymentModalOpen.set(true);
  }

  confirmOnlineUnapaiedPaymentCancel() {
    this.closeOnlineUnapaiedPaymentConfirmModal();
    this.paymentService
      .cancelPayment((this.booking()?.payment as IPayment)?.id!)
      .subscribe({
        next: () => {
          this.onCancel.emit();
        },
        error: () => {
          this.toastService.showError("Errore nell'annullamento del pagamento");
        },
      });
  }

  formatDate(date: string): string {
    // Formatta la data con date-fns
    const formattedDate = format(new Date(date), 'EEEE d MMMM yyyy', {
      locale: it,
    });

    // Capitalizza la prima lettera del giorno
    return formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);
  }

  /**
   * Estrae il giorno dalla data
   */
  getDayFromDate(date: string): string {
    return format(new Date(date), 'd', { locale: it });
  }

  /**
   * Estrae il mese dalla data in formato abbreviato e lo capitalizza
   */
  getMonthFromDate(date: string): string {
    const month = format(new Date(date), 'MMM', { locale: it });
    return month.toUpperCase();
  }

  getDayOfWeek(date: string): string {
    const dayOfWeek = format(new Date(date), 'EEEE', { locale: it });
    return dayOfWeek.charAt(0).toUpperCase() + dayOfWeek.slice(1);
  }

  payNow() {
    this.bookingService.setCurrentBooking(this.booking());
    const paymentId = (<IPayment>this.booking()?.payment)?.id;
    if (!paymentId)
      return console.error('Nessun pagamento trovato nel booking');
    this.router.navigate(['pay-now', paymentId]);
  }

  goToRefund() {
    this.bookingService.setCurrentBooking(this.booking());
    const paymentId = (<IPayment>this.booking()?.payment)?.id;
    if (!paymentId)
      return console.error('Nessun pagamento trovato nel booking');
    this.router.navigate(['refund', paymentId]);
  }

  openReceipt() {
    this.paymentService
      .downloadReceipt((this.booking()?.payment as IPayment)?.id!)
      .subscribe((res) => {
        window.open(res.data?.receiptUrl!, '_blank');
      });
  }

  edit() {
    this.onEdit.emit();
  }
}
