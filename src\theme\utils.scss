.flex {
    display: flex;

    &-justify-start {
        justify-content: flex-start;
    }

    &-justify-center {
        justify-content: center;
    }

    &-justify-end {
        justify-content: flex-end;
    }

    &-justify-between {
        justify-content: space-between;
    }

    &-justify-around {
        justify-content: space-around;
    }

    &-justify-evenly {
        justify-content: space-evenly;
    }

    &-align-start {
        align-items: flex-start;
    }

    &-align-center {
        align-items: center;
    }

    &-align-end {
        align-items: flex-end;
    }

    &-align-stretch {
        align-items: stretch;
    }
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mt-4 {
    margin-top: 4px !important;
}

.img-contain {
    object-fit: contain;
}