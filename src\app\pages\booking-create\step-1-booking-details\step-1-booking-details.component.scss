// Stili per la card di riepilogo
.summary-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  background-color: #f5f9ff;
}

// Stili per i titoli delle sezioni
.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-top: 20px;
  margin-bottom: 10px;
  color: #333;

  &:not(:first-of-type) {
    margin-top: 40px;
  }
}

// Stili per il select personalizzato
.custom-select,
.custom-date-input {
  --background: white;
  border-radius: 12px;
  margin-bottom: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

// Stili per il selettore nascosto
.hidden-select {
  position: absolute;
  opacity: 0;
  height: 0;
  width: 0;
  overflow: hidden;
}

// Nasconde la freccia a destra predefinita di Ionic
.custom-item {
  --inner-border-width: 0;

  &::part(detail-icon) {
    display: none !important;
  }
}

// Stili per gli slot orari
.time-slots {
  display: grid;
  // 4 col autofit grid-template-columns
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 4px;
  margin-bottom: 20px;
}

.time-slot {
  --border-radius: 8px;
  --border-color: #e0e0e0;
  --color: #333;
  --background: white;
  --background-hover: #f5f5f5;
  --background-activated: rgba(
    0,
    0,
    0,
    0.1
  ); /* Sfondo più scuro quando premuto */
  --background-activated-opacity: 1; /* Opacità piena per l'effetto */
  min-width: 60px;

  &.selected {
    --background: var(--ion-color-primary);
    --color: white;
    --border-color: var(--ion-color-primary);
    --background-hover: var(--ion-color-primary);
    --background-activated: var(--ion-color-primary);
    --background-activated-opacity: 1; /* Opacità piena per l'effetto */
  }
}

.empty-slot {
  color: #999;
  grid-column: 1/-1;
  margin-top: 0;
}

// Spazio extra per evitare che il contenuto venga nascosto dal pulsante fisso
.bottom-spacer {
  height: 80px;
}

// Stili per il calendario
ion-datetime {
  --background: white;
  --calendar-background: white;
  --title-color: #333;
  --placeholder-color: #999;
}

// Stili per la modale del calendario
ion-modal {
  --border-radius: 16px;
  --box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

// Stili specifici per la modale del calendario
.calendar-modal {
  --height: 80%;
  --width: 100%;
  --offset-y: -20%;
}

// Stili per il contenuto della modale del calendario
.calendar-modal-content {
  --padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Stili per il calendario centrato
.centered-calendar {
  width: 100%;
  max-width: 350px;
  margin: 20px auto 0;

  // Stili per la visualizzazione del calendario
  --calendar-width: 100%;

  // Stili per i giorni del calendario
  --calendar-day-size: 40px;
}

ion-item.custom-item {
  --padding-end: 0;
}

.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  grid-column: 1/-1;
  height: 150px;
  background-color: transparent;
}

.custom-spinner {
  width: 60px;
  height: 60px;
  --color: var(--ion-color-primary);
  background-color: transparent;
}
