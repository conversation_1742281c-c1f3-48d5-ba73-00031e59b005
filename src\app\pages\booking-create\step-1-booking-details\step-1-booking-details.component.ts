import { Component, signal, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { trackEvent } from '@aptabase/web';
import { BookingService } from '@core/services/booking.service';
import { StaffService } from '@core/services/staff.service';
import { getDateFormatByDate } from '@core/utils/date';
import {
  IonButton,
  IonContent,
  IonDatetime,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonModal,
  IonSelect,
  IonSelectOption,
  IonSpinner,
  IonTitle,
  IonToolbar,
  NavController,
} from '@ionic/angular/standalone';
import { AppbarComponent } from '@shared/appbar/appbar.component';
import { add, format } from 'date-fns';
import { it } from 'date-fns/locale';
import { addIcons } from 'ionicons';
import { calendarOutline, chevronDown, person } from 'ionicons/icons';
import { mergeMap, tap } from 'rxjs';

@Component({
  selector: 'app-step-1-booking-details',
  templateUrl: './step-1-booking-details.component.html',
  styleUrls: ['./step-1-booking-details.component.scss'],
  standalone: true,
  imports: [
    IonToolbar,
    IonContent,
    IonList,
    IonItem,
    IonLabel,
    IonSelect,
    IonSelectOption,
    IonButton,
    IonIcon,
    IonDatetime,
    IonModal,
    FormsModule,
    AppbarComponent,
    IonSpinner,
    IonHeader,
    IonTitle,
  ],
})
export class Step1BookingDetailsComponent {
  @ViewChild('operatorSelect') operatorSelect: any;
  protected selectedOperator = signal<string | null>(null);
  protected isLoading = signal<boolean>(true);

  protected selectedDate = signal<string | null>(null);
  protected formattedDate = signal<string>(format(new Date(), 'dd/MM/yyyy'));
  protected selectedTime = signal<string | null>(null);

  protected availableTimes = signal<string[]>([]);
  protected disabledDates = signal<string[]>([]);
  protected minDate = signal<string>(format(new Date(), 'yyyy-MM-dd'));
  protected maxDate = signal<string>(
    format(add(new Date(), { years: 1 }), 'yyyy-MM-dd'),
  );
  protected staffList = this.staffService.staff;

  protected isDateModalOpen = signal<boolean>(false);

  constructor(
    private navCtrl: NavController,
    private staffService: StaffService,
    private bookingService: BookingService,
  ) {
    trackEvent('step_1_booking_details_page');
    addIcons({
      chevronDown,
      calendarOutline,
      person,
    });
  }

  ionViewWillEnter() {
    this.isLoading.set(true);
    this.fetchStaffListAndDisabledDates();
  }

  fetchStaffListAndDisabledDates() {
    const today = new Date();
    this.staffService
      .getStaff()
      .pipe(
        tap((res) => {
          this.selectedOperator.set(res.data![0].id);
        }),
        mergeMap(() => {
          return this.staffService
            .getCalendarDisabledDates(
              //format should be YYYY-MM-DD
              getDateFormatByDate(today),
              getDateFormatByDate(add(today, { years: 1 })),
              this.selectedOperator()!,
            )
            .pipe(
              tap((res) => {
                this.disabledDates.set(res.data!);
                this.setInitialDate();
                // Dopo aver impostato la data iniziale, carichiamo gli slot disponibili
                if (this.selectedDate()) {
                  this.loadAvailableSlots();
                }
                this.isLoading.set(false);
              }),
            );
        }),
      )
      .subscribe();
  }

  /**
   * Imposta la prima data disponibile (non disabilitata) come data selezionata
   * Se la data odierna è disponibile, viene selezionata quella
   * Altrimenti viene selezionata la prima data disponibile successiva
   */
  setInitialDate() {
    // Se abbiamo già una data selezionata (da precompilazione), non facciamo nulla
    if (this.selectedDate()) {
      return;
    }

    const today = new Date();
    const disabledDates = this.disabledDates();

    // Verifica se la data odierna è disponibile
    const todayFormatted = format(today, 'yyyy-MM-dd');
    if (!disabledDates.includes(todayFormatted)) {
      // La data odierna è disponibile, la impostiamo
      this.selectedDate.set(todayFormatted);
      this.formattedDate.set(format(today, 'dd/MM/yyyy', { locale: it }));
      return;
    }

    // La data odierna non è disponibile, cerchiamo la prima data disponibile
    let currentDate = today;
    let found = false;

    // Cerchiamo per i prossimi 365 giorni (un anno)
    for (let i = 1; i <= 365 && !found; i++) {
      currentDate = add(today, { days: i });
      const dateFormatted = format(currentDate, 'yyyy-MM-dd');

      if (!disabledDates.includes(dateFormatted)) {
        // Abbiamo trovato una data disponibile
        this.selectedDate.set(dateFormatted);
        this.formattedDate.set(
          format(currentDate, 'dd/MM/yyyy', { locale: it }),
        );
        found = true;
      }
    }

    // Se non abbiamo trovato date disponibili, manteniamo la data odierna
    if (!found) {
      this.selectedDate.set(todayFormatted);
      this.formattedDate.set(format(today, 'dd/MM/yyyy', { locale: it }));
    }
  }

  onOperatorChange(event: CustomEvent) {
    this.selectedOperator.set(event.detail.value);
    this.loadAvailableSlots();
  }

  getSelectedOperatorLabel(): string {
    const operatorId = this.selectedOperator();
    if (!operatorId || !this.staffList()) return 'Seleziona un operatore';

    const selectedOperator = this.staffList()?.find(
      (op) => op.id === operatorId,
    );
    return selectedOperator
      ? `${selectedOperator.name} ${selectedOperator.surname}`
      : 'Seleziona un operatore';
  }

  openOperatorSelect() {
    if (this.operatorSelect) {
      this.operatorSelect.open();
    }
  }

  onDateChange(event: CustomEvent) {
    const selectedDate = event.detail.value;
    this.selectedDate.set(selectedDate);
    this.formattedDate.set(
      format(new Date(selectedDate), 'dd/MM/yyyy', { locale: it }),
    );
    this.isDateModalOpen.set(false);
    this.loadAvailableSlots();
  }

  onTimeSelect(time: string) {
    this.selectedTime.set(time);
  }

  isDateEnabled = (dateString: string) => {
    const formattedDate = format(new Date(dateString), 'yyyy-MM-dd');
    const disabledDates = this.disabledDates();

    // Verifica se la data è nella lista delle date disabilitate
    return !disabledDates.includes(formattedDate);
  };

  /**
   * Carica gli slot disponibili per l'operatore e la data selezionati
   */
  loadAvailableSlots() {
    const operatorId = this.selectedOperator();
    const date = this.selectedDate();

    if (operatorId && date) {
      this.isLoading.set(true);
      this.staffService
        .getBookingAvailableSlots(
          date,
          this.bookingService.currentBooking()?.services?.map((s) => s.id)!,
          operatorId,
        )
        .subscribe({
          next: (res) => {
            this.availableTimes.set(res.data!);
            this.selectedTime.set(null);
            this.isLoading.set(false);
          },
        });
    }
  }

  onContinueClick() {
    this.isLoading.set(true);
    if (this.selectedOperator() && this.selectedDate() && this.selectedTime()) {
      // Salva i dati nel servizio
      this.bookingService.setDate(this.selectedDate()!);
      this.bookingService.setTime(this.selectedTime()!);

      // // Salva l'operatore selezionato
      const operatorId = this.selectedOperator()!;
      const selectedOperator = this.staffList()?.find(
        (op) => op.id === operatorId,
      );
      if (selectedOperator) {
        this.bookingService.setStaff(selectedOperator);
      }

      // Naviga alla prossima pagina
      this.navCtrl.navigateForward('/booking/user-details').then(() => {
        this.isLoading.set(false);
      });
    }
  }
}
