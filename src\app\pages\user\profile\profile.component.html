<app-appbar
  [title]="'Profilo'"
  [showBackButton]="true"
  [isLoading]="isLoading()"
></app-appbar>

<ion-content>
  <div class="profile-header">
    <ion-avatar class="profile-avatar">
      <img
        alt="Silhouette of a person's head"
        src="https://ionicframework.com/docs/img/demos/avatar.svg"
      />
    </ion-avatar>
  </div>
  <div class="profile-info">
    <ion-text>
      <p>{{ user()?.name }} {{ user()?.surname }}</p>
    </ion-text>
  </div>
  <div class="statistics-container">
    <ion-card>
      <ion-card-content>
        <ion-grid>
          <ion-row>
            <ion-col>
              <div class="icon-container">
                <span class="value">{{ totalBookings() }}</span>
              </div>
              <ion-text class="text">Prenotazioni</ion-text>
            </ion-col>
            <ion-col>
              <div class="icon-container">
                <span class="value">{{ totalServices() }}</span>
              </div>
              <ion-text class="text">Servizi</ion-text>
            </ion-col>
            <ion-col>
              <div class="icon-container">
                <span class="value">{{ totalCancellations() }}</span>
              </div>
              <ion-text class="text">Cancellazioni</ion-text>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-card-content>
    </ion-card>
  </div>
  @if (modifyProfile()) {
    <form [formGroup]="baseForm" class="ion-padding pt-0 pb-0">
      <h2 class="mb-0">Dati profilo</h2>
      <ion-list class="input-list">
        <ion-item lines="none" class="custom-item">
          <ion-icon
            size="small"
            slot="start"
            name="person"
            aria-hidden="true"
          ></ion-icon>
          <ion-input
            formControlName="name"
            type="text"
            placeholder="Inserisci nome"
          ></ion-input>
        </ion-item>
        <ion-item lines="none" class="custom-item">
          <ion-icon
            size="small"
            slot="start"
            name="person"
            aria-hidden="true"
          ></ion-icon>
          <ion-input
            formControlName="surname"
            type="text"
            placeholder="Inserisci cognome"
          ></ion-input>
        </ion-item>
        <ion-item lines="none" class="custom-item">
          <ion-icon
            size="small"
            slot="start"
            name="call"
            aria-hidden="true"
          ></ion-icon>
          <ion-input
            formControlName="mobilePhone"
            type="tel"
            placeholder="Inserisci numero di telefono"
          ></ion-input>
        </ion-item>
      </ion-list>
    </form>
  } @else {
    <div class="ion-padding pt-0 pb-0">
      <ion-list style="border-radius: 8px">
        <ion-item>
          <ion-icon
            size="small"
            slot="start"
            name="mail"
            aria-hidden="true"
          ></ion-icon>
          <ion-label>{{ user()?.email }}</ion-label>
        </ion-item>
        <ion-item>
          <ion-icon
            size="small"
            slot="start"
            name="person"
            aria-hidden="true"
          ></ion-icon>
          <ion-label>{{ user()?.name }}</ion-label>
        </ion-item>
        <ion-item>
          <ion-icon
            size="small"
            slot="start"
            name="person"
            aria-hidden="true"
          ></ion-icon>
          <ion-label>{{ user()?.surname }}</ion-label>
        </ion-item>
        <ion-item>
          <ion-icon
            size="small"
            slot="start"
            name="call"
            aria-hidden="true"
          ></ion-icon>
          <ion-label>{{ user()?.mobilePhone }}</ion-label>
        </ion-item>
      </ion-list>
    </div>
  }

  <div class="ion-padding pt-0">
    @if (!modifyProfile()) {
      <ion-button
        color="primary"
        expand="block"
        [disabled]="isLoading()"
        (click)="onModifyProfile()"
      >
        Modifica
      </ion-button>
    } @else {
      <ion-button
        expand="block"
        [disabled]="baseForm.invalid || isLoading()"
        (click)="onUpdateClick()"
      >
        Aggiorna Profilo
      </ion-button>
    }

    <!-- Elementi fissi in basso -->
    <div class="logout-button">
      <ion-text class="ion-text-center">
        <a class="danger" (click)="onLogout()">Logout</a>
      </ion-text>
    </div>
  </div>
</ion-content>
