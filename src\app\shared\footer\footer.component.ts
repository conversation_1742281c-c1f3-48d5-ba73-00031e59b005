import { Component, inject, signal } from '@angular/core';
import { SettingsService } from '@core/services/settings.service';
import { IonIcon, IonImg } from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import {
  callOutline,
  informationCircleOutline,
  locationOutline,
} from 'ionicons/icons';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  imports: [IonIcon, IonImg],
})
export class FooterComponent {
  private settingService = inject(SettingsService);
  protected companyInfo = this.settingService.companyInfo;
  protected companyLocation = this.settingService.shortCompanyAddress;
  protected currentYear = signal<number>(new Date().getFullYear());
  public isModalOpen = signal<boolean>(true);

  constructor() {
    addIcons({
      callOutline,
      locationOutline,
      informationCircleOutline,
    });
  }
}
