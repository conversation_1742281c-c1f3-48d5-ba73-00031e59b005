<app-appbar
  [title]="'Area personale'"
  [showBackButton]="true"
  [isLoading]="isLoading()"
  (onBackButtonClick)="goToHome()"
></app-appbar>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="handleRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>
  <ion-list [inset]="true">
    <ion-item [button]="true" (click)="onProfileClick()"> Profilo</ion-item>
    <ion-item [button]="true" (click)="onPastBookingsClick()">
      Appuntamenti passati</ion-item
    >
  </ion-list>

  <ion-list-header>Prossimi Appuntamenti</ion-list-header>
  @if (!isLoading()) {
    @for (booking of bookings(); track booking.id) {
      <div style="margin-bottom: 40px">
        <app-booking-card
          [booking]="booking"
          (onCancel)="onDeleteBooking(booking)"
          (onEdit)="onEditBooking(booking)"
        ></app-booking-card>
      </div>
    } @empty {
      <div class="empty-state">
        <img
          width="50%"
          height="100%"
          src="/assets/images/no-bookings.svg"
          alt="Nessun appuntamento in programma"
        />
        <p>Nessun appuntamento in programma</p>
        <ion-button (click)="goToHome()" class="button">
          Prenota un appuntamento
        </ion-button>
      </div>
    }
  } @else {
    <div class="spinner-container">
      <ion-spinner name="dots" class="custom-spinner"></ion-spinner>
    </div>
  }

  <ion-infinite-scroll (ionInfinite)="loadMoreData($event)">
    <ion-infinite-scroll-content
      loadingSpinner="bubbles"
      loadingText="Caricamento in corso..."
    ></ion-infinite-scroll-content>
  </ion-infinite-scroll>

  <ion-modal
    (didDismiss)="setOpen(false)"
    [isOpen]="isModalOpen()"
    [initialBreakpoint]="0.7"
    [breakpoints]="[0.7, 1]"
  >
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-title>Impostazioni</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setOpen(false)" [strong]="true"
              >Chiudi</ion-button
            >
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content>
        <ion-list-header>Tema</ion-list-header>
        <ion-list [inset]="true">
          <ion-item>
            <ion-toggle
              [ngModel]="paletteToggle()"
              (ionChange)="toggleChange($event)"
              justify="space-between"
              >Modalità scura</ion-toggle
            >
          </ion-item>
        </ion-list>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
