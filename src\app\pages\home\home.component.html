<app-appbar
  [company]="companyInfo()!"
  [mode]="'home'"
  [rightIcon]="'person-circle-outline'"
  (onRightIconClick)="onProfileClick()"
  [isLoading]="isLoading()"
  [user]="userInfo()"
></app-appbar>

<ion-content class="container">
  <ion-toolbar>
    <ion-text>
      <h1 class="ion-padding-start ion-padding-end">
        <strong>Prenota un appuntamento</strong>
      </h1>
    </ion-text>
    <p class="subtitle ion-padding-start ion-padding-end">
      Seleziona uno o più servizi che desideri prenotare
    </p>
  </ion-toolbar>

  <div class="services-section">
    <div class="custom-search-container">
      <div class="search-icon">
        <ion-icon name="search-outline" aria-label="Cerca servizio"></ion-icon>
      </div>
      <input
        type="text"
        placeholder="Cerca un servizio..."
        class="custom-search-input"
        ngModel
        (ngModelChange)="onSearchChange($event)"
      />
    </div>
    <div class="services-container">
      @for (service of filteredServices(); track service.id) {
        <app-service-item
          [service]="service"
          [isSelected]="isServiceSelected(service)"
          (onItemSelected)="manageSelectedServices($event)"
        ></app-service-item>
      } @empty {
        <div class="empty-state">
          <img
            width="50%"
            height="100%"
            src="/assets/images/empty-services.svg"
            alt="Nessun servizio trovato"
          />
          <p>Ops! sembra che non ci siano servizi disponibili</p>
        </div>
      }
    </div>
  </div>

  <!-- Spazio extra per evitare che il contenuto venga nascosto dal pulsante fisso -->
  <div class="bottom-spacer"></div>
  <!-- <app-footer></app-footer> -->
</ion-content>

<!-- Pulsante fisso in basso -->
<div class="fixed-bottom-button">
  <ion-button
    shape="round"
    (click)="onBookClick()"
    expand="block"
    class="custom-button"
    [disabled]="selectedServices().length === 0"
    aria-label="Prenota appuntamento"
  >
    <span>Prenota</span>
    @if (selectedServices().length > 0) {
      <div class="counter-badge" aria-label="Numero servizi selezionati">
        {{ selectedServices().length }}
      </div>
    }
  </ion-button>
  <div class="footer">
    <p class="vat-number ion-padding-start ion-padding-end">
      P.I. {{ companyInfo()?.vatNumber }}
    </p>
    <ion-icon
      name="chevron-up-outline"
      (click)="isDateModalOpen.set(true)"
      aria-label="Apri calendario"
      tabindex="0"
      role="button"
    ></ion-icon>
  </div>
</div>

<ion-modal
  [isOpen]="isDateModalOpen()"
  (willDismiss)="isDateModalOpen.set(false)"
  [initialBreakpoint]="0.7"
  [breakpoints]="[0.1, 0.7, 1]"
  [backdropDismiss]="true"
  [canDismiss]="true"
  class="calendar-modal"
>
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>Informazioni</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="closeModal()" strong="true">Chiudi</ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content>
      <app-footer></app-footer>
    </ion-content>
  </ng-template>
</ion-modal>
