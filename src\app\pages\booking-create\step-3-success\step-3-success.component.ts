import { HttpClient } from '@angular/common/http';
import { Component, inject, signal } from '@angular/core';
import { trackEvent } from '@aptabase/web';
import { BookingService } from '@core/services/booking.service';
import { ServicesService } from '@core/services/services.service';
import { environment } from '@env/environment';
import {
  IonButton,
  IonContent,
  IonIcon,
  NavController,
} from '@ionic/angular/standalone';
import { BookingSummaryComponent } from '@shared/booking-summary/booking-summary.component';
import { addIcons } from 'ionicons';
import { calendarOutline, checkmarkCircle } from 'ionicons/icons';

@Component({
  selector: 'app-step-3-success',
  templateUrl: './step-3-success.component.html',
  styleUrls: ['./step-3-success.component.scss'],
  standalone: true,
  imports: [IonContent, IonButton, IonIcon, BookingSummaryComponent],
})
export class Step3SuccessComponent {
  private bookingService = inject(BookingService);
  private servicesService = inject(ServicesService);
  protected currentBooking = this.bookingService.currentBooking;
  protected isLoading = signal<boolean>(false);

  constructor(
    private navCtrl: NavController,
    private http: HttpClient,
  ) {
    trackEvent('step_3_success_page');
    addIcons({
      checkmarkCircle,
      calendarOutline,
    });
  }

  addToCalendar() {
    this.isLoading.set(true);

    const url = `${environment.api.bookings}/${this.currentBooking()?.id!}/download`;

    this.http.get(url, { responseType: 'blob' }).subscribe({
      next: (response: Blob) => {
        const blobUrl = URL.createObjectURL(response);
        const a = document.createElement('a');
        a.href = blobUrl;
        a.download = 'event.ics';
        a.click();
        a.remove();
        URL.revokeObjectURL(blobUrl); // Rilascia l'URL Blob
        this.isLoading.set(false);
      },
      error: (err) => {
        console.error('Errore durante il download:', err);
      },
    });
  }

  goToHome() {
    this.isLoading.set(true);
    this.bookingService.clearCurrentBooking();
    this.servicesService.clearSelectedServices();
    this.navCtrl.navigateRoot('/').then(() => {
      this.isLoading.set(false);
    });
  }
}
