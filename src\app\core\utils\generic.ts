export class GenericUtils {
  static AUTH_DATA = 'auth_data';
  static session_token = 'session_token';
  static refresh_token = 'refreshToken';
  static user_id = 'user_id';
  static tenantID = 'x-tenant-id';

  static generateUniqueId(): string {
    // Generate a random part (you can customize the length as needed)
    const randomPart = Math.random().toString(36).substring(2, 10);

    // Get the current timestamp
    const timestamp = new Date().getTime().toString(36);

    // Combine timestamp and random part
    const uniqueId = timestamp + randomPart;

    return uniqueId;
  }

  static toStripeAmount(amount: number): number {
    const stripeAmount = Math.round(amount * 100);
    return stripeAmount >= 50 ? stripeAmount : 50; // Ensure minimum is 50 cents
  }
}
