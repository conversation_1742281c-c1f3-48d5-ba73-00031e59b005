import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { GenericUtils } from '@core/utils/generic';
import { environment } from '@env/environment';
import { NavController } from '@ionic/angular/standalone';
import {
  IAuthResponse,
  IRegisterResponse,
} from '@models/interfaces/auth-response';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { IBooking } from '@models/interfaces/booking';
import { IRequestFilter, IRequestMeta } from '@models/interfaces/request';
import { IUser } from '@models/interfaces/user';
import { switchMap, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private _baseAuthApi = environment.api.auth!;
  private _baseUserApi = environment.api.users!;
  private _isLoggedIn = signal<boolean>(false);
  public isLoggedIn = this._isLoggedIn.asReadonly();
  private _user = signal<Partial<IUser> | undefined>(undefined);
  public user = this._user.asReadonly();

  constructor(
    private http: HttpClient,
    private navCtrl: NavController,
  ) {}

  register(userData: {
    email: string;
    name: string;
    surname: string;
    mobilePhone: string;
  }) {
    return this.http.post<IBaseResponse<IRegisterResponse>>(
      `${this._baseAuthApi}/register`,
      userData,
    );
  }

  setIsLoggedIn(isLoggedIn: boolean) {
    this._isLoggedIn.set(isLoggedIn);
  }

  setUser(user: Partial<IUser> | undefined) {
    this._user.set(user);
  }

  login(email: string) {
    return this.http
      .post<IBaseResponse<IAuthResponse>>(`${this._baseAuthApi}/login`, {
        email,
      })
      .pipe(
        tap((res) => {
          if (res.data?.email) this._user.set(res.data);
        }),
      );
  }

  getUserProfile(id: string) {
    return this.http
      .get<IBaseResponse<Partial<IUser>>>(`${this._baseUserApi}/${id}/profile`)
      .pipe(tap((res) => this.setUser(res.data)));
  }

  updateUserProfile(id: string, user: Partial<IUser>) {
    return this.http
      .put<
        IBaseResponse<Partial<IUser>>
      >(`${this._baseUserApi}/${id}/profile`, user)
      .pipe(tap((res) => this.setUser(res.data)));
  }

  searchUserBookings(
    id: string,
    meta: IRequestMeta,
    filter?: IRequestFilter[],
  ) {
    return this.http.post<IBaseResponse<IBooking[], IFindResponseMeta>>(
      `${this._baseUserApi}/${id}/bookings`,
      {
        meta,
        filter,
      },
    );
  }

  confirmOtp(email: string, password: string) {
    return this.http
      .post<any>(`${this._baseAuthApi}/confirm-otp`, {
        email,
        password,
      })
      .pipe(
        tap((res) => {
          localStorage.setItem(GenericUtils.session_token, res.data!.token);
          localStorage.setItem(GenericUtils.user_id, res.data!.id);
        }),
        switchMap((res) =>
          this.getUserProfile(res.data!.id).pipe(
            tap((res) => this._user.set(res.data!)),
          ),
        ),
        tap(() => this._isLoggedIn.set(true)),
      );
  }

  logout() {
    return this.http
      .get<IBaseResponse<void>>(`${this._baseAuthApi}/logout`)
      .pipe(
        tap(() => {
          this.clearStorage();
          this.navCtrl.navigateRoot(['/']);
        }),
      );
  }

  clearStorage() {
    this._isLoggedIn.set(false);
    this._user.set(undefined);
    localStorage.removeItem(GenericUtils.session_token);
    localStorage.removeItem(GenericUtils.user_id);
  }

  refreshToken() {
    return this.http
      .get<
        IBaseResponse<{ token: string }>
      >(`${this._baseAuthApi}/refresh-token`)
      .pipe(
        tap((res) => {
          localStorage.setItem(GenericUtils.session_token, res.data!.token);
        }),
      );
  }
}
