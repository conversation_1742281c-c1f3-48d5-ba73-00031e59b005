import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
import { IBaseResponse } from '@models/interfaces/base-response';
import { IStaff } from '@models/interfaces/staff';
import { tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class StaffService {
  private _baseStaffApi = environment.api.staff!;
  private _staff = signal<IStaff[] | undefined>(undefined);
  public staff = this._staff.asReadonly();

  private _availableSlots = signal<string[]>([]);
  public availableSlots = this._availableSlots.asReadonly();

  setStaff(staff: IStaff[]) {
    this._staff.set(staff);
  }
  constructor(private http: HttpClient) { }

  setAvaibileSlots(slots: string[]) {
    this._availableSlots.set(slots);
  }

  getStaff() {
    return this.http.get<IBaseResponse<IStaff[]>>(`${this._baseStaffApi}`).pipe(
      tap(res => {
        this.setStaff(res.data!);
      })
    );
  }

  getCalendarDisabledDates(
    startDate: string,
    endDate: string,
    staffId: string
  ) {
    return this.http.post<IBaseResponse<string[]>>(
      `${this._baseStaffApi}/${staffId}/disabled-dates`,
      { startDate: startDate, endDate: endDate }
    );
  }

  getBookingAvailableSlots(date: string, services: string[], staffId: string) {
    return this.http
      .post<IBaseResponse<string[]>>(
        `${this._baseStaffApi}/${staffId}/available-slots`,
        {
          date: date,
          services: services
        }
      )
      .pipe(tap(res => this.setAvaibileSlots(res.data!)));
  }
}
