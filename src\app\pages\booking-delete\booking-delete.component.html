<app-appbar
  [title]="'Annulla prenotazione'"
  [showBackButton]="false"
  [isLoading]="isLoading()"
></app-appbar>
<ion-content>
  @if (!isLoading() && !bookingError()) {
    <div class="content">
      <div class="image-container">
        <img
          src="/assets/images/delete-booking.svg"
          alt="Booking delete"
          width="70%"
          height="auto"
          class="booking-delete-image"
        />
      </div>
      <div class="ion-padding-top ion-padding-bottom text">
        <h3>Ti confermiamo che l'appuntamento è stato annullato.</h3>
      </div>
    </div>
  } @else if (bookingError()) {
    <div class="content">
      <div class="image-container">
        <ion-icon name="close-circle" color="danger"></ion-icon>
      </div>
      <div class="ion-padding-horizontal text">
        <h3>
          Si è verificato un errore durante l'annullamento della prenotazione,
          <br />
          ti invitiamo a riprovare.
        </h3>
      </div>
    </div>
  } @else {
    <div class="loader-spinner-container">
      <ion-spinner name="crescent"></ion-spinner>
    </div>
  }

  @if (!isLoading()) {
    <div class="fixed-bottom-button">
      <ion-button
        expand="block"
        [disabled]="isLoading()"
        (click)="onHomeClick()"
      >
        Torna alla Home
      </ion-button>
    </div>
  }
</ion-content>
