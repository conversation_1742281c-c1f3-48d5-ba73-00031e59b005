@defer (when !stripeLoading()) {
  @if (this.stripeElementsOptions.clientSecret) {
    <ngx-stripe-elements
      class="stripe"
      #elements
      [stripe]="stripe"
      [elementsOptions]="stripeElementsOptions"
    >
      <ngx-stripe-payment />
      <div #target id="target" style="height: 100px"></div>
    </ngx-stripe-elements>
  } @else {
    <div class="payment-error">
      Impossibile completare il pagamento. Riprova più tardi.
    </div>
  }
} @placeholder {
  <div class="loader-spinner-container">
    <ion-spinner name="dots" class="custom-spinner"></ion-spinner>
  </div>
}
