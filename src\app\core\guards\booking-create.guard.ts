import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { NavController } from '@ionic/angular';
import { BookingService } from '../services/booking.service';

export const bookingCreateGuard: CanActivateFn = (route, state) => {
  const bookingService = inject(BookingService);
  const navCtrl = inject(NavController);
  // Use state.url instead of route.url mapping
  const fullPath = state.url;

  // Check if we're being redirected from payment provider
  const isPaymentRedirect =
    route.queryParams['payment_intent'] && route.queryParams['redirect_status'];
  // Step 1
  if (route.url[0].path === 'booking') {
    if (bookingService.currentBooking()?.services?.length! > 0) {
      return true;
    }
  }

  // Step 2
  if (route.url[0].path === 'booking/user-details') {
    if (bookingService.currentBooking()?.user?.email) {
      return true;
    }
  }

  // Handle payment redirect
  if (state.url.includes('booking/user-details') && isPaymentRedirect) {
    return true;
  }

  // Step 3
  if (route.url[0].path === 'booking/success') {
    if (bookingService.currentBooking()?.payment) {
      return true;
    }
  }

  return navCtrl.navigateForward('/');
};
