<app-appbar
  [title]="'Quando?'"
  [showBackButton]="true"
  [isLoading]="isLoading()"
></app-appbar>
<ion-content>
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Quando?</ion-title>
      <p class="subtitle ion-padding-start ion-padding-end mb-0">
        Seleziona un membro dello staff e la data
      </p>
    </ion-toolbar>
  </ion-header>
  <!-- Selezione operatore -->
  <div class="ion-padding">
    <h1 class="section-title">Seleziona staff</h1>
    <ion-list class="input-list">
      <ion-item
        lines="none"
        class="custom-item"
        button
        (click)="openOperatorSelect()"
      >
        <ion-icon slot="start" name="person" aria-hidden="true"></ion-icon>
        <ion-label>{{ getSelectedOperatorLabel() }}</ion-label>
        <ion-icon
          slot="end"
          name="chevron-down"
          size="small"
          style="margin-right: 0"
        ></ion-icon>
      </ion-item>
      <ion-select
        #operatorSelect
        interface="action-sheet"
        placeholder="Seleziona un operatore"
        [value]="selectedOperator()"
        (ionChange)="onOperatorChange($event)"
        [disabled]="isLoading()"
        class="hidden-select"
      >
        @for (operator of staffList(); track operator.id) {
          <ion-select-option [value]="operator.id"
            >{{ operator.name }} {{ operator.surname }}</ion-select-option
          >
        }
      </ion-select>
    </ion-list>

    <!-- Selezione data -->
    <h2 class="section-title">Seleziona la data</h2>
    <ion-list class="input-list">
      <ion-item
        lines="none"
        class="custom-item"
        (click)="isLoading() ? null : isDateModalOpen.set(true)"
        button
      >
        <ion-icon
          slot="start"
          name="calendar-outline"
          aria-hidden="true"
        ></ion-icon>
        <ion-label>{{ formattedDate() }}</ion-label>
        <ion-icon
          slot="end"
          name="chevron-down"
          size="small"
          style="margin-right: 0"
        ></ion-icon>
      </ion-item>
    </ion-list>

    <!-- Modal per il calendario -->
    <ion-modal
      [isOpen]="isDateModalOpen()"
      (willDismiss)="isDateModalOpen.set(false)"
      [initialBreakpoint]="0.7"
      [breakpoints]="[0.7, 1]"
      [backdropDismiss]="true"
      [backdropBreakpoint]="0.5"
      class="calendar-modal"
    >
      <ng-template>
        <ion-content class="calendar-modal-content">
          <ion-datetime
            class="centered-calendar"
            presentation="date"
            [value]="selectedDate()"
            (ionChange)="onDateChange($event)"
            locale="it-IT"
            showDefaultButtons="true"
            doneText="Conferma"
            cancelText="Annulla"
            [isDateEnabled]="isDateEnabled"
            [min]="minDate()"
            [max]="maxDate()"
          ></ion-datetime>
        </ion-content>
      </ng-template>
    </ion-modal>

    <!-- Selezione orario -->
    <h2 class="section-title">Seleziona l'orario</h2>
    <div class="time-slots">
      @if (isLoading()) {
        <div class="spinner-container">
          <ion-spinner name="dots" class="custom-spinner"></ion-spinner>
        </div>
      } @else {
        @for (time of availableTimes(); track time) {
          <ion-button
            class="time-slot"
            [class.selected]="selectedTime() === time"
            (click)="onTimeSelect(time)"
            fill="outline"
          >
            {{ time }}
          </ion-button>
        } @empty {
          <p class="empty-slot">Nessun orario disponibile</p>
        }
      }
    </div>
  </div>
  <!-- Spazio extra per evitare che il contenuto venga nascosto dal pulsante fisso -->
  <div class="bottom-spacer"></div>
</ion-content>

<!-- Pulsante fisso in basso -->
<div class="fixed-bottom-button">
  <ion-button
    expand="block"
    class="custom-button"
    [disabled]="
      !selectedOperator() || !selectedDate() || !selectedTime() || isLoading()
    "
    (click)="onContinueClick()"
  >
    Continua
  </ion-button>
</div>
