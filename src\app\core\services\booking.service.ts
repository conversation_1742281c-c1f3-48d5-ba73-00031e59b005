import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
import { IBaseResponse } from '@models/interfaces/base-response';
import {
  IBooking,
  IBookingRequest,
  IService,
} from '@models/interfaces/booking';
import { paymentMethodType } from '@models/interfaces/payment';
import { IStaff } from '@models/interfaces/staff';
import { IUser } from '@models/interfaces/user';
import { tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class BookingService {
  private _baseBookingApi = environment.api.bookings!;
  private _baseServiceApi = environment.api.services!;

  private _currentBooking = signal<Partial<IBooking> | undefined>(undefined);

  public currentBooking = this._currentBooking.asReadonly();

  private _isLoading = signal<boolean>(false);
  public isLoading = this._isLoading.asReadonly();

  private _bookingError = signal<boolean>(false);
  public bookingError = this._bookingError.asReadonly();

  constructor(private http: HttpClient) {}

  setPaymentMethod(paymentMethod: paymentMethodType) {
    this._currentBooking.update((prev) => ({
      ...prev,
      payment: paymentMethod,
    }));
  }

  setUserId(userId: string) {
    this._currentBooking.update((prev) => ({
      ...prev,
      user: {
        ...prev!.user!,

        id: userId,
      },
    }));
  }
  setStaff(staff: IStaff) {
    this._currentBooking.update((prev) => ({
      ...prev,
      staff: staff,
    }));
  }

  setIsLoading(isLoading: boolean) {
    this._isLoading.set(isLoading);
  }

  setBookingError(bookingError: boolean) {
    this._bookingError.set(bookingError);
  }

  clearCurrentBooking() {
    this.setCurrentBooking(undefined);
    //this.selectedServices.set([]);
  }

  setServices(services: IService[]) {
    this._currentBooking.update((prev) => ({
      ...prev,
      services: services,
      totalPrice: services.reduce((sum, service) => sum + service.price, 0),
      totalDuration: services.reduce(
        (sum, service) => sum + service.duration,
        0,
      ),
    }));
  }

  updateServices(services: IService[]) {
    this._currentBooking.update((prev) => ({
      ...prev,
      services: services,
      totalPrice: services.reduce((sum, service) => sum + service.price, 0),
      totalDuration: services.reduce(
        (sum, service) => sum + service.duration,
        0,
      ),
    }));
  }

  getServices() {
    return this.http.get<IBaseResponse<IService[]>>(this._baseServiceApi).pipe(
      tap((res) => {
        this.setServices(res.data!);
      }),
    );
  }
  setTime(time: string) {
    this._currentBooking.update((prev) => ({ ...prev, startTime: time }));
  }

  setUserDetail(user: IUser) {
    this._currentBooking.update((prev) => ({ ...prev, user: user }));
  }

  setDate(date: string) {
    this._currentBooking.update((prev) => ({ ...prev, date: date }));
  }

  setCurrentBooking(booking: IBooking | undefined) {
    this._currentBooking.set(booking);
  }

  addToCalendar(bookingId: string) {
    return this.http.get<string>(
      `${this._baseBookingApi}/${bookingId}/download`,
    );
  }

  createBooking(booking: IBooking) {
    return this.http
      .post<
        IBaseResponse<IBooking>
      >(this._baseBookingApi, this.transformBookingIntoBookingRequest(booking))
      .pipe(tap((res) => this.setCurrentBooking(res.data!)));
  }

  updatedBooking(booking: Partial<IBooking>, bookingId: string) {
    return this.http.put<IBaseResponse<void>>(
      `${this._baseBookingApi}/${bookingId}`,
      this.transformBookingIntoBookingRequest(booking as IBooking),
    );
  }

  deleteBooking(bookingId: string) {
    return this.http.delete<IBaseResponse<void>>(
      `${this._baseBookingApi}/${bookingId}`,
    );
  }

  getBookingById(bookingId: string) {
    return this.http.get<IBaseResponse<IBooking>>(
      `${this._baseBookingApi}/${bookingId}`,
    );
  }

  transformBookingIntoBookingRequest(booking: IBooking): IBookingRequest {
    return {
      id: booking!.id,
      user: booking!.user,
      date: booking!.date,
      startTime: booking!.startTime,
      endTime: booking!.endTime,
      status: booking!.status,
      services: booking!.services!.map((s) => s.id),
      totalPrice: booking!.totalPrice,
      payment: booking!.payment,
      totalDuration: booking!.totalDuration,
      staff: booking!.staff!.id,
    } as IBookingRequest;
  }
}
