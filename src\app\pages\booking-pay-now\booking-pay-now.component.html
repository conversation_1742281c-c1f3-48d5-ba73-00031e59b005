<app-appbar
  [title]="'Quando?'"
  [showBackButton]="true"
  [isLoading]="isLoading()"
></app-appbar>
<ion-content>
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Paga ora</ion-title>
      <p class="subtitle ion-padding-start ion-padding-end mb-0">
        Compila i campi per completare la prenotazione
      </p>
    </ion-toolbar>

    <div class="ion-padding">
      <app-stripe
        #stripe
        [mode]="stripeMode.payNow"
        [paymentId]="paymentId()"
        (isStripeReady)="isStripeReady.set($event)"
        [onPayClick]="onPayClick()"
        (onPaySuccess)="onStripePaySuccess($event)"
        (onPayFailed)="onStripePayFailed()"
      />

      <div class="fixed-bottom-button">
        <app-stripe-button (onClick)="onPayClick.set(true)" />
      </div>
    </div>
  </ion-header>
</ion-content>
