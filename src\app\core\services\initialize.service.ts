import { inject, Injectable } from '@angular/core';
import { AuthService } from '@core/services/auth.service';
import { ServicesService } from '@core/services/services.service';
import { SettingsService } from '@core/services/settings.service';
import { StaffService } from '@core/services/staff.service';
import { environment } from '@env/environment';
import { NavController } from '@ionic/angular';
import { catchError, forkJoin, Observable, of, switchMap } from 'rxjs';
import { GenericUtils } from '../utils/generic';
@Injectable({
  providedIn: 'root',
})
export class InitializeService {
  protected authStore = inject(AuthService);
  protected servicesService = inject(ServicesService);
  protected staffService = inject(StaffService);
  protected settingsService = inject(SettingsService);
  protected navCtrl = inject(NavController);

  constructor() {}

  initApp(): Observable<any> {
    // Estrae solo la prima parte del dominio (es: "test-prova" da "test-prova.ciao.it")
    const url: string = !environment.production
      ? 'bellezza-boutique-dev'
      : window.location.hostname.split('.')[0];
    const userId = localStorage.getItem(GenericUtils.user_id);
    const userToken = localStorage.getItem(GenericUtils.session_token);
    const requests$: Observable<any>[] = [
      this.settingsService.getCompanyInfo(),
      this.servicesService.getServices(),
    ];

    if (!!userId && !!userToken) {
      requests$.push(this.authStore.getUserProfile(userId));
      this.authStore.setIsLoggedIn(true);
    } else {
      this.authStore.clearStorage();
    }

    return this.settingsService.getTenantId(url).pipe(
      switchMap(() => forkJoin(requests$)),
      catchError((err) => {
        console.log(
          "Errore durante l'inizializzazione dell'app: Tenant id non trovato",
        );
        this.navCtrl.navigateRoot('/salon-not-found');
        return of(true);
      }),
    );
  }
}
