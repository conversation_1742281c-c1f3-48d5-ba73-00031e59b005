import {
  Component,
  effect,
  ElementRef,
  input,
  OnInit,
  output,
  signal,
  untracked,
  viewChild,
} from '@angular/core';
import { BookingService } from '@core/services/booking.service';
import { PaymentsService } from '@core/services/payment.service';
import { ToastService } from '@core/services/toast.service';
import { environment } from '@env/environment';
import { IonSpinner } from '@ionic/angular/standalone';
import { StripeElementsOptions } from '@stripe/stripe-js';
import {
  injectStripe,
  StripeElementsDirective,
  StripePaymentElementComponent,
} from 'ngx-stripe';
import { v4 as uuid } from 'uuid';

export enum stripeMode {
  newPayment = 'new-payment',
  payNow = 'pay-now',
}

@Component({
  selector: 'app-stripe',
  imports: [
    /** Directives */
    StripeElementsDirective,
    /** Components */
    StripePaymentElementComponent,
    IonSpinner,
  ],
  templateUrl: './stripe.component.html',
  styleUrl: './stripe.component.scss',
})
export class StripeComponent implements OnInit {
  readonly stripe = injectStripe(environment.stripeKey);
  protected stripeElementsOptions: StripeElementsOptions = {
    locale: 'it',
    appearance: {
      theme: 'flat',
      labels: 'floating',
      variables: {
        colorPrimary: '#1e3d8e',
        colorBackground: '#ffffff',
        colorText: '#30313d',
        colorDanger: '#df1b41',
        spacingUnit: '4px',
        borderRadius: '12px',
      },
    },
  };

  protected currentBooking = this.bookingService.currentBooking;
  protected stripeLoading = signal(true);
  paymentElement = viewChild(StripePaymentElementComponent);
  targetScroll = viewChild<ElementRef>('target');

  protected paymentStripeSet = effect(() => {
    if (this.targetScroll()) {
      setTimeout(() => {
        this.#scrollToStripe();
        document.getElementById('target')?.remove();
      }, 1000);
    }
  });

  readonly mode = input.required<stripeMode>();
  readonly paymentId = input<string | null>();

  readonly onPaySuccess = output<string>();
  readonly onPayFailed = output<void>();
  readonly onPayClick = input<boolean>();
  readonly isStripeReady = output<boolean>();

  protected onPaymentIntent = effect(() => {
    if (this.onPayClick()) {
      untracked(() => this.pay());
    }
  });

  constructor(
    private bookingService: BookingService,
    private paymentsService: PaymentsService,
    private toastService: ToastService,
  ) {
    this.paymentsService.paymentvalid.set(false);
  }

  ngOnInit(): void {
    this.handleMode();
  }

  handleMode() {
    if (this.mode() === stripeMode.newPayment) {
      const idempotencyKey = uuid();
      this.paymentsService
        .createPayment(
          this.currentBooking()?.user?.id!,
          this.currentBooking()?.id!,
          idempotencyKey,
        )
        .subscribe({
          next: (paymentIntent) => {
            this.stripeElementsOptions.clientSecret =
              paymentIntent.data.client_secret;
            this.stripeLoading.set(false);
            this.paymentsService.paymentvalid.set(true);
            this.isStripeReady.emit(true);
            console.log('paymentIntent', paymentIntent);
          },
          error: (err) => {
            console.error('Error creating payment intent', err);
          },
        });
    }
    if (this.mode() === stripeMode.payNow) {
      //TODO Get payment intent from paymentId
      this.paymentsService.getPaymentIntent(this.paymentId()!).subscribe({
        next: (paymentIntent) => {
          console.log('paymentIntent', paymentIntent);
          this.stripeElementsOptions.clientSecret =
            paymentIntent?.data?.client_secret!;
          this.stripeLoading.set(false);
          this.paymentsService.paymentvalid.set(true);
          this.isStripeReady.emit(true);
        },
        error: (err) => {
          console.error('Error creating payment intent', err);
        },
      });
    }
  }

  pay() {
    const location = window.location.origin;
    console.log('pay', this.currentBooking());
    this.stripe
      .confirmPayment({
        elements: this.paymentElement()!.elements,
        confirmParams: {
          payment_method_data: {
            billing_details: {
              name: this.currentBooking()?.user?.name,
              email: this.currentBooking()?.user?.email,
              phone: this.currentBooking()?.user?.mobilePhone,
            },
          },
          return_url: `${location}/booking/user-details`,
        },
        redirect: 'if_required',
      })
      .subscribe({
        next: (result) => {
          if (result.error) {
            console.error('Payment failed', result.error);
            this.toastService.showError('Pagamento rifiutato');
            this.onPayFailed.emit();
          } else {
            console.log('Payment succeeded', result.paymentIntent);
            this.onPaySuccess.emit(result.paymentIntent?.id!);
          }
        },
        error: (err) => {
          console.error('Error confirming payment', err);
          this.toastService.showError('Pagamento rifiutato');
          this.onPayFailed.emit();
        },
      });
  }

  #scrollToStripe(): void {
    this.targetScroll()?.nativeElement.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    });
  }
}
