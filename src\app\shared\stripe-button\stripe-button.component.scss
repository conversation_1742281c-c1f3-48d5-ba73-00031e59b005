// <PERSON><PERSON> per le icone all'interno del bottone
.credit-card-icon {
  font-size: 20px;
  margin-right: 4px;
}

.stripe-icon {
  font-size: 46px;
  margin-left: 4px;
  margin-top: 2px;
}

// <PERSON><PERSON> per la variante "pay" - bottone pieno
:host .stripe-button-pay {
  --background: #635bff !important;
  --background-hover: #4f46f8 !important;
  --background-activated: #3f37f5 !important;
  --color: white !important;
  --color-hover: white !important;
  --color-activated: white !important;
  --padding-start: 16px !important;
  --padding-end: 16px !important;
  --padding-top: 12px !important;
  --padding-bottom: 12px !important;
  --border-radius: 8px !important;
  --box-shadow: none !important;
  --transition: all 0.3s ease !important;

  font-weight: 500 !important;
  width: auto !important;
  min-width: fit-content !important;
}

// Stili per la variante "manage" - bottone outline
:host .stripe-button-manage {
  --background: transparent !important;
  --background-hover: #f8f9fa !important;
  --background-activated: rgba(99, 91, 255, 0.1) !important;
  --color: #635bff !important;
  --color-hover: #4f46f8 !important;
  --color-activated: #3f37f5 !important;
  --border-color: #635bff !important;
  --border-style: solid !important;
  --border-width: 1px !important;
  --padding-start: 16px !important;
  --padding-end: 16px !important;
  --padding-top: 12px !important;
  --padding-bottom: 12px !important;
  --border-radius: 8px !important;
  --box-shadow: none !important;
  --transition: all 0.3s ease !important;

  font-weight: 500 !important;
  width: auto !important;
  min-width: fit-content !important;
}
