// Stili comuni per le icone
.credit-card-icon {
  font-size: 20px;
  margin-right: 4px;
}

.stripe-icon {
  font-size: 46px;
  margin-left: 4px;
  margin-top: 2px;
}

// Variante "pay" - bottone pieno con colori Stripe
.stripe-button-pay {
  // CSS Custom Properties di Ionic per il background
  --background: #635bff;
  --background-hover: #4f46f8;
  --background-activated: #3f37f5;

  // CSS Custom Properties di Ionic per il colore del testo
  --color: white;
  --color-hover: white;
  --color-activated: white;

  // CSS Custom Properties di Ionic per padding e dimensioni
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;

  // CSS Custom Properties di Ionic per bordi e forma
  --border-radius: 8px;
  --box-shadow: none;

  // CSS Custom Properties di Ionic per transizioni
  --transition: all 0.3s ease;

  // Stili CSS normali per proprietà non coperte dalle custom properties
  font-weight: 500;
  width: auto;
  min-width: fit-content;
}

// Variante "manage" - bottone outline
.stripe-button-manage {
  // CSS Custom Properties di Ionic per il background
  --background: transparent;
  --background-hover: #f8f9fa;
  --background-activated: rgba(99, 91, 255, 0.1);

  // CSS Custom Properties di Ionic per il colore del testo
  --color: #635bff;
  --color-hover: #4f46f8;
  --color-activated: #3f37f5;

  // CSS Custom Properties di Ionic per i bordi
  --border-color: #635bff;
  --border-style: solid;
  --border-width: 1px;

  // CSS Custom Properties di Ionic per padding e dimensioni
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;

  // CSS Custom Properties di Ionic per bordi e forma
  --border-radius: 8px;
  --box-shadow: none;

  // CSS Custom Properties di Ionic per transizioni
  --transition: all 0.3s ease;

  // Stili CSS normali per proprietà non coperte dalle custom properties
  font-weight: 500;
  width: auto;
  min-width: fit-content;
}
