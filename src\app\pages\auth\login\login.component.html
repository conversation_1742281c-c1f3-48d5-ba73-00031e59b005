<app-appbar
  [title]="'Accedi'"
  [showBackButton]="true"
  [isLoading]="isLoading()"
></app-appbar>

<ion-content>
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Accedi</ion-title>
      <p class="subtitle ion-padding-start ion-padding-end mb-0">
        Se sei un utente registrato o hai effettuato una prenotazione in
        passato, puoi accedere al tuo account.
      </p>
    </ion-toolbar>
  </ion-header>
  <form [formGroup]="baseForm" class="ion-padding">
    <ion-list class="input-list">
      <ion-item lines="none" class="custom-item">
        <ion-icon slot="start" name="mail" aria-hidden="true"></ion-icon>
        <ion-input
          placeholder="Inserisci email"
          formControlName="email"
          type="email"
          [disabled]="isLoading()"
        ></ion-input>
      </ion-item>
    </ion-list>
  </form>

  <!-- Spazio extra per evitare che il contenuto venga nascosto dagli elementi fissi -->
  <div class="bottom-spacer"></div>
</ion-content>

<!-- Elementi fissi in basso -->
<div class="fixed-bottom-elements">
  <ion-button
    expand="block"
    (click)="onSubmitClick()"
    [disabled]="baseForm.invalid || isLoading()"
    >Accedi</ion-button
  >

  <ion-text class="ion-text-center">
    <p>
      Non hai un account?
      <a (click)="onRegisterClick()">Registrati</a>
    </p>
  </ion-text>
</div>
