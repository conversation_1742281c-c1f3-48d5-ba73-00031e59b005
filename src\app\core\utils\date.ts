export const customIsSameDay = (date1: Date, date2: Date): boolean => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

// CONVERT HOUR STRING TO DATE
export const createDateFromTime = (timeString: string): Date => {
  const [hours, minutes] = timeString.split(':').map(Number);

  const date = convertTZ(new Date());
  date.setHours(hours, minutes, 0, 0);
  return date;
};

export const createNewDateFromDateAndTime = (date: string, time: string) => {
  return convertTZ(new Date(`${date}T${time}`));
};

export const convertTZ = (date: string | Date) => {
  return new Date(
    new Date(date).toLocaleString('en-US', { timeZone: 'Europe/Rome' }),
  );
};

export const getDateFormatByDate = (date: Date): string => {
  if (!date) return '';

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

export const getTimeFormatByDate = (date: Date): string => {
  if (!date) {
    return '';
  }
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
};

export const addMinutesToTime = (
  start: string,
  minutesToAdd: number = 30,
): string => {
  const [hours, minutes] = start.split(':').map(Number);
  const date = new Date();
  date.setHours(hours, minutes); // Set initial time to the date object
  date.setMinutes(date.getMinutes() + minutesToAdd); // Add the specified minutes

  // Ensure the HH:mm format with leading zeros where necessary
  const newHours = String(date.getHours()).padStart(2, '0');
  const newMinutes = String(date.getMinutes()).padStart(2, '0');

  return `${newHours}:${newMinutes}`;
};

export const transformDailyOpenHours = (formValue: any) => {
  const { dailyOpenHours } = formValue;

  const filtered = dailyOpenHours.filter((day: any) => day.enabled);

  return filtered.map((day: any) => {
    return {
      dayOfWeek: day.dayOfWeek,
      timeSlots: day.timeSlots.map((slot: any) => {
        return {
          start: getTimeFormatByDate(slot.start),
          end: getTimeFormatByDate(slot.end),
        };
      }),
    };
  });
};
