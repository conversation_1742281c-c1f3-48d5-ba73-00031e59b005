/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
// @import "@ionic/angular/css/palettes/dark.system.css";
@import "@ionic/angular/css/palettes/dark.class.css";
/* Ionic Variables and Theming
 * ---------------------------------------------------------------
 * Any overrides to theme variables should be placed in this file.
 * For more information, please see:
 * http://ionicframework.com/docs/theming/
 */

@import "./theme/splash-screen.scss";
@import "./theme/variables.scss";
@import "./theme/utils.scss";

/* Stile per liste personalizzate */
.input-list {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
}

/* Stile per item personalizzati */
.custom-item {
  --border-style: none;
  --min-height: 40px;
  --padding-start: 16px;
  --padding-end: 10px;
  --padding-top: 4px;
  --padding-bottom: 6px;

  ion-icon {
    color: var(--ion-color-medium);
    font-size: 24px;
    margin-right: 12px;
  }
}

/* Rimuove i bordi degli input */

/* Le variabili dei colori sono state spostate in theme/variables.scss */

/* Stile per searchbar più alta */
ion-searchbar.custom {
  height: 80px !important;

  .searchbar-input-container {
    height: 80px !important;
  }

  .searchbar-input {
    height: 80px !important;
    padding-top: 25px !important;
    padding-bottom: 25px !important;
    font-size: 18px !important;
  }

  .searchbar-search-icon {
    width: 24px !important;
    height: 24px !important;
    top: 28px !important;
  }
}

// Stili per il pulsante fisso in basso
.fixed-bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  z-index: 999;
}
.custom-button {
  --background: var(--ion-color-primary);
  --background-activated: rgba(
    0,
    0,
    0,
    0.1
  ); /* Sfondo più scuro quando premuto */
  --background-activated-opacity: 1; /* Opacità piena per l'effetto */
  --background-hover: var(
    --ion-color-primary
  ); /* Mantiene il colore primario per hover */
  --border-radius: 8px;
  margin: 0;
  height: 50px;
  font-weight: 600;
}

// Stili per i pulsanti nelle sezioni fixed-bottom-elements
.fixed-bottom-elements ion-button {
  --background-activated: rgba(
    0,
    0,
    0,
    0.1
  ); /* Sfondo più scuro quando premuto */
  --background-activated-opacity: 1; /* Opacità piena per l'effetto */
}

.header-ios ion-toolbar:last-of-type {
  --border-width: 0;
}

ion-datetime {
  background: none;
}