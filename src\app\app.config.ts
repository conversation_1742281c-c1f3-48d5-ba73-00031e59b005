import {
  ApplicationConfig,
  importProvidersFrom,
  inject,
  provideAppInitializer,
} from '@angular/core';
import { provideAnimations } from '@angular/platform-browser/animations';
import {
  InMemoryScrollingOptions,
  provideRouter,
  RouteReuseStrategy,
  withComponentInputBinding,
  withInMemoryScrolling,
} from '@angular/router';

import { routes } from './app.routes';

// Importa queste due linee
import { registerLocaleData } from '@angular/common';
import {
  HttpClient,
  provideHttpClient,
  withFetch,
  withInterceptors,
} from '@angular/common/http';
import localeIt from '@angular/common/locales/it';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { errorInterceptor } from '@core/interceptors/error.interceptor';
import { jwtInterceptor } from '@core/interceptors/jwt.interceptor';
import { requestInterceptor } from '@core/interceptors/request.interceptor';
import { InitializeService } from '@core/services/initialize.service';
import {
  IonicRouteStrategy,
  provideIonicAngular,
} from '@ionic/angular/standalone';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { provideNgxStripe } from 'ngx-stripe';
import { Observable } from 'rxjs';
// Registra i dati locali
registerLocaleData(localeIt);

const scrollConfig: InMemoryScrollingOptions = {
  scrollPositionRestoration: 'top',
  anchorScrolling: 'enabled',
};

export const appConfig: ApplicationConfig = {
  providers: [
    {
      provide: RouteReuseStrategy,
      useClass: IonicRouteStrategy,
    },
    provideAppInitializer(() => {
      const initializeService = inject(InitializeService);
      return initializeApp(initializeService);
    }),
    provideIonicAngular({
      mode: 'ios',
      hardwareBackButton: true,
      animated: false,
    }),
    provideAnimationsAsync(),
    provideRouter(
      routes,
      withInMemoryScrolling(scrollConfig),
      withComponentInputBinding(),
    ),
    provideHttpClient(
      withFetch(),
      withInterceptors([requestInterceptor, errorInterceptor, jwtInterceptor]),
    ),
    importProvidersFrom(
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: TranslateLoaderFn,
          deps: [HttpClient],
        },
      }),
    ),
    provideAnimations(),
    provideNgxStripe(process.env.STRIPE_PUBLISHABLE_KEY),
  ],
};

export function TranslateLoaderFn(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

export function initializeApp(appService: InitializeService): Observable<any> {
  return appService.initApp();
}
