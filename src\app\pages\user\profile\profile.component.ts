import { Component, effect, signal, untracked } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/auth.service';
import { ToastService } from '@core/services/toast.service';
import { CustomValidators } from '@core/validators/custom.validator';
import {
  IonAvatar,
  IonButton,
  IonCard,
  IonCardContent,
  IonCol,
  IonContent,
  IonGrid,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonList,
  IonRow,
  IonText,
} from '@ionic/angular/standalone';
import { IUser } from '@models/interfaces/user';
import { AppbarComponent } from '@shared/appbar/appbar.component';
import { addIcons } from 'ionicons';
import { calendar, call, mail, person, statsChart } from 'ionicons/icons';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    IonList,
    IonItem,
    IonInput,
    IonButton,
    IonIcon,
    IonContent,
    AppbarComponent,
    IonCard,
    IonCardContent,
    IonAvatar,
    IonCol,
    IonGrid,
    IonRow,
    IonText,
    IonLabel,
  ],
})
export class ProfileComponent {
  protected baseForm!: FormGroup;
  protected isLoading = signal<boolean>(true);
  protected user = this.authService.user;
  protected totalBookings = signal<number>(0);
  protected totalServices = signal<number>(0);
  protected totalCancellations = signal<number>(0);
  protected modifyProfile = signal<boolean>(false);

  protected userEffect = effect(() => {
    if (this.user()) {
      this.baseForm.patchValue(this.user()!);
      this.totalBookings.set(this.user()!.totalBookings!);
      this.totalServices.set(this.user()!.totalServices!);
      this.totalCancellations.set(this.user()!.totalCancelledBookings!);
      this.baseForm.get('email')?.disable();
      untracked(() => this.isLoading.set(false));
    }
  });

  constructor(
    private authService: AuthService,
    private fb: FormBuilder,
    private toastService: ToastService,
  ) {
    trackEvent('profile_page');
    this.initForm();
    addIcons({
      mail,
      person,
      call,
      calendar,
      statsChart,
    });
  }

  onModifyProfile() {
    this.modifyProfile.set(true);
  }

  onCancelModifyProfile() {
    this.modifyProfile.set(false);
  }

  initForm() {
    this.baseForm = this.fb.group({
      id: [this.user()!.id, [Validators.required]],
      email: [
        null,
        [Validators.required, Validators.pattern(CustomValidators.emailRegex)],
      ],
      name: [null, [Validators.required, Validators.minLength(2)]],
      surname: [null, [Validators.required, Validators.minLength(2)]],
      mobilePhone: [
        null,
        [
          Validators.required,
          Validators.minLength(10),
          Validators.minLength(10),
        ],
      ],
    });
  }

  onUpdateClick() {
    this.isLoading.set(true);
    let userDetail: Partial<IUser> = this.baseForm.getRawValue();

    this.authService.updateUserProfile(this.user()!.id!, userDetail).subscribe({
      next: () => {
        this.toastService.showSuccess('Dati aggiornati con successo!');
        this.isLoading.set(false);
        this.onCancelModifyProfile();
      },
      error: () => {
        this.isLoading.set(false);
      },
    });
  }

  onLogout() {
    this.authService.logout().subscribe();
  }
}
