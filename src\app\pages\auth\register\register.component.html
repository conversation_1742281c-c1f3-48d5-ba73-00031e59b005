<app-appbar
  [title]="'Registrazione'"
  [showBackButton]="true"
  [isLoading]="isLoading()"
></app-appbar>

<ion-content>
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Registrazione</ion-title>
      <p class="subtitle ion-padding-start ion-padding-end mb-0">
        Inserisci i tuoi dati per registrarti. Non è necessario impostare la
        password, per accedere ti invieremo un codice via e-mail.
      </p>
    </ion-toolbar>
  </ion-header>
  <form [formGroup]="baseForm" class="ion-padding">
    <ion-list class="input-list">
      <ion-item lines="none" class="custom-item">
        <ion-icon slot="start" name="mail" aria-hidden="true"></ion-icon>
        <ion-input
          formControlName="email"
          type="email"
          placeholder="Inserisci email"
          [disabled]="isLoading()"
        ></ion-input>
      </ion-item>
    </ion-list>

    <ion-list class="input-list">
      <ion-item lines="none" class="custom-item">
        <ion-icon slot="start" name="person" aria-hidden="true"></ion-icon>
        <ion-input
          formControlName="name"
          type="text"
          placeholder="Inserisci nome"
          [disabled]="isLoading()"
        ></ion-input>
      </ion-item>
    </ion-list>

    <ion-list class="input-list">
      <ion-item lines="none" class="custom-item">
        <ion-icon slot="start" name="person" aria-hidden="true"></ion-icon>
        <ion-input
          formControlName="surname"
          type="text"
          placeholder="Inserisci cognome"
          [disabled]="isLoading()"
        ></ion-input>
      </ion-item>
    </ion-list>

    <ion-list class="input-list">
      <ion-item lines="none" class="custom-item">
        <ion-icon slot="start" name="call" aria-hidden="true"></ion-icon>
        <ion-input
          formControlName="mobilePhone"
          type="tel"
          placeholder="Inserisci numero di telefono"
          [disabled]="isLoading()"
        ></ion-input>
      </ion-item>
    </ion-list>
  </form>

  <!-- Spazio extra per evitare che il contenuto venga nascosto dagli elementi fissi -->
  <div class="bottom-spacer"></div>
</ion-content>

<!-- Elementi fissi in basso -->
<div class="fixed-bottom-elements">
  <ion-button
    expand="block"
    (click)="onRegisterClick()"
    [disabled]="baseForm.invalid || isLoading()"
    >Registrati</ion-button
  >

  <ion-text class="ion-text-center">
    <p>
      Hai già un account?
      <a (click)="onLoginClick()">Accedi</a>
    </p>
  </ion-text>
</div>
