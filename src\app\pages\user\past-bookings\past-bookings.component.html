<app-appbar
  [title]="'Appuntamenti passati'"
  [showBackButton]="true"
  [isLoading]="isLoading()"
></app-appbar>

<ion-content>
  <ion-refresher slot="fixed" (ionRefresh)="handleRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  @if (!isLoading()) {
    @for (booking of bookings(); track booking.id) {
      <div style="margin-bottom: 40px">
        <app-booking-card [booking]="booking"></app-booking-card>
      </div>
    } @empty {
      <div class="empty-state">
        <img
          width="50%"
          height="100%"
          src="/assets/images/no-past-bookings.svg"
          alt="Nessun appuntamento passato"
        />
        <p>Non hai appuntamenti passati</p>
        <ion-button (click)="goToHome()" class="button">
          Prenota un appuntamento
        </ion-button>
      </div>
    }
  } @else {
    <div class="spinner-container">
      <ion-spinner name="dots" class="custom-spinner"></ion-spinner>
    </div>
  }

  <ion-infinite-scroll (ionInfinite)="loadMoreData($event)">
    <ion-infinite-scroll-content
      loadingSpinner="bubbles"
      loadingText="Caricamento in corso..."
    ></ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
