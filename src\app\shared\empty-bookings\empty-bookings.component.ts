import {
  ChangeDetectionStrategy,
  Component,
  input,
  output,
} from '@angular/core';
import { IonButton, IonIcon } from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { calendarOutline } from 'ionicons/icons';

@Component({
  selector: 'app-empty-bookings',
  templateUrl: './empty-bookings.component.html',
  styleUrls: ['./empty-bookings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IonIcon, IonButton],
})
export class EmptyBookingsComponent {
  // Input per personalizzare il messaggio
  message = input<string>('Non hai appuntamenti prenotati');

  // Input per personalizzare il testo del bottone
  buttonText = input<string>('Prenota Ora');

  // Output per l'evento di click sul bottone
  onButtonClick = output<void>();

  constructor() {
    // Registra le icone necessarie
    addIcons({
      calendarOutline,
    });
  }
}
