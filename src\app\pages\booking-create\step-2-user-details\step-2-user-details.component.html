<app-appbar
  [title]="'Per chi?'"
  [showBackButton]="true"
  [isLoading]="isLoading()"
></app-appbar>

<ion-content>
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Per chi?</ion-title>
      <p class="subtitle ion-padding-start ion-padding-end mb-0">
        <span>Compila i campi per completare la prenotazione</span>
        <br />
        @if (authService.isLoggedIn()) {
          <span style="font-size: 12px"
            >I campi sono precompilati con i tuoi dati</span
          >
        }
      </p>
    </ion-toolbar>
  </ion-header>
  <div class="ion-padding">
    <form [formGroup]="userForm">
      <ion-list class="input-list">
        <ion-item lines="none" class="custom-item">
          <ion-icon slot="start" name="mail" aria-hidden="true"></ion-icon>
          <ion-input
            formControlName="email"
            type="email"
            placeholder="Inserisci email"
          ></ion-input>
        </ion-item>
      </ion-list>
      <ion-list class="input-list">
        <ion-item lines="none" class="custom-item">
          <ion-icon slot="start" name="person" aria-hidden="true"></ion-icon>
          <ion-input
            formControlName="name"
            type="text"
            placeholder="Inserisci nome"
          ></ion-input>
        </ion-item>
      </ion-list>

      <ion-list class="input-list">
        <ion-item lines="none" class="custom-item">
          <ion-icon slot="start" name="person" aria-hidden="true"></ion-icon>
          <ion-input
            formControlName="surname"
            type="text"
            placeholder="Inserisci cognome"
          ></ion-input>
        </ion-item>
      </ion-list>

      <ion-list class="input-list">
        <ion-item lines="none" class="custom-item">
          <ion-icon slot="start" name="call" aria-hidden="true"></ion-icon>
          <ion-input
            formControlName="mobilePhone"
            minlength="10"
            maxlength="10"
            type="tel"
            placeholder="Inserisci numero di telefono"
          ></ion-input>
        </ion-item>
      </ion-list>
    </form>

    <!-- Sezione metodo di pagamento -->
    <h2 class="section-title">Seleziona metodo di pagamento</h2>
    <ion-list class="input-list">
      <ion-radio-group
        [value]="selectedPayment()?.method"
        (ionChange)="selectPaymentMethod($event.detail.value)"
      >
        @for (method of paymentMethods(); track method.method) {
          <ion-item lines="none" class="custom-item payment-method-item">
            <ion-icon
              slot="start"
              [name]="method.icon"
              aria-hidden="true"
            ></ion-icon>
            <ion-label>{{ method.name }}</ion-label>
            <ion-radio slot="end" [value]="method.method"></ion-radio>
          </ion-item>
        }
      </ion-radio-group>
    </ion-list>
    @if (stripeContent()) {
      <app-stripe
        #stripe
        [mode]="stripeMode.newPayment"
        (isStripeReady)="isStripeReady.set($event)"
        [onPayClick]="onPayClick()"
        (onPaySuccess)="onStripePaySuccess($event)"
        (onPayFailed)="onStripePayFailed()"
      />
    }
    <!-- @switch (selectedPayment()?.method) {
      @case (paymentMethodType.card) {
        
      }
    } -->
    <!-- Spazio extra per evitare che il contenuto venga nascosto dal pulsante fisso -->
    <div class="bottom-spacer"></div>
  </div>
</ion-content>

<!-- Pulsante fisso in basso -->
<div class="fixed-bottom-button">
  @if (stripePayButton()) {
    <app-stripe-button (onClick)="onPayClick.set(true)" />
  } @else {
    <ion-button
      expand="block"
      class="booking-button"
      [disabled]="
        (userForm.enabled && !userForm.valid) ||
        !selectedPayment() ||
        isLoading() ||
        !paymentAllowed()
      "
      (click)="onBookClick()"
    >
      {{ buttonTitle() }}
    </ion-button>
  }
</div>
<app-booking-summary
  [booking]="currentBooking()!"
  [showPane]="true"
></app-booking-summary>
