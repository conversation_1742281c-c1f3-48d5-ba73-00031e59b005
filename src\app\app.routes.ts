import { Routes } from '@angular/router';
import { authGuard } from '@core/guards/auth.guard';
import { bookingCreateGuard } from '@core/guards/booking-create.guard';
import { loggedGuard } from '@core/guards/logged.guard';
import { otpGuard } from '@core/guards/otp.guard';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./pages/home/<USER>').then((m) => m.HomeComponent),
  },
  {
    path: 'auth',
    title: 'Autenticazione',
    children: [
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full',
      },
      {
        path: 'login',
        loadComponent: () =>
          import('./pages/auth/login/login.component').then(
            (m) => m.LoginComponent,
          ),
        title: 'Login',
      },
      {
        path: 'register',
        loadComponent: () =>
          import('./pages/auth/register/register.component').then(
            (m) => m.RegisterComponent,
          ),
        title: 'Registrazione',
      },
      {
        path: 'verify-code',
        loadComponent: () =>
          import('./pages/auth/verify-otp/verify-otp.component').then(
            (m) => m.VerifyOtpComponent,
          ),
        canActivate: [otpGuard],
        title: 'Verifica codice',
      },
    ],
    canActivate: [loggedGuard],
  },
  {
    path: 'user',
    title: 'Utente',
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./pages/user/user.component').then((m) => m.UserComponent),
        canActivate: [authGuard],
        title: 'Profilo utente',
      },
      {
        path: 'profile',
        loadComponent: () =>
          import('./pages/user/profile/profile.component').then(
            (m) => m.ProfileComponent,
          ),
        canActivate: [authGuard],
        title: 'Profilo',
      },
      {
        path: 'past-bookings',
        loadComponent: () =>
          import('./pages/user/past-bookings/past-bookings.component').then(
            (m) => m.PastBookingsComponent,
          ),
        canActivate: [authGuard],
        title: 'Prenotazioni passate',
      },
    ],
  },
  {
    path: 'booking',
    title: 'Prenotazione',
    children: [
      {
        path: '',
        redirectTo: 'booking-details',
        pathMatch: 'full',
      },
      {
        path: 'booking-details',
        loadComponent: () =>
          import(
            './pages/booking-create/step-1-booking-details/step-1-booking-details.component'
          ).then((m) => m.Step1BookingDetailsComponent),
        title: 'Dettagli prenotazione',
      },
      {
        path: 'user-details',
        loadComponent: () =>
          import(
            './pages/booking-create/step-2-user-details/step-2-user-details.component'
          ).then((m) => m.Step2UserDetailsComponent),
        title: 'Dati utente',
      },
      {
        path: 'success',
        loadComponent: () =>
          import(
            './pages/booking-create/step-3-success/step-3-success.component'
          ).then((m) => m.Step3SuccessComponent),
        title: 'Prenotazione completata',
      },
    ],
    canActivate: [bookingCreateGuard],
  },
  {
    path: 'delete-booking/:bookingId',
    loadComponent: () =>
      import('./pages/booking-delete/booking-delete.component').then(
        (m) => m.BookingDeleteComponent,
      ),
    title: 'Elimina prenotazione',
  },
  {
    path: 'pay-now/:paymentId',
    loadComponent: () =>
      import('./pages/booking-pay-now/booking-pay-now.component').then(
        (m) => m.BookingPayNowComponent,
      ),
    title: 'Paga ora',
  },
  {
    path: 'refund/:paymentId',
    loadComponent: () =>
      import('./pages/booking-refund/booking-refund.component').then(
        (m) => m.BookingRefundComponent,
      ),
    title: 'Rimborso',
  },
  {
    path: 'salon-not-found',
    loadComponent: () =>
      import('./pages/salon-not-found/salon-not-found.component').then(
        (m) => m.SalonNotFoundComponent,
      ),
    title: 'Salone non trovato',
  },
  {
    path: '**',
    redirectTo: '',
    pathMatch: 'full',
    title: 'Pagina non trovata',
  },
];
