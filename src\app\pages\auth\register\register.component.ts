import { Component, signal } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/auth.service';
import { ToastService } from '@core/services/toast.service';
import { CustomValidators } from '@core/validators/custom.validator';
import {
  IonButton,
  IonContent,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonList,
  IonText,
  IonTitle,
  IonToolbar,
  NavController,
} from '@ionic/angular/standalone';
import { AppbarComponent } from '@shared/appbar/appbar.component';
import { addIcons } from 'ionicons';
import { call, mail, person } from 'ionicons/icons';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss'],
  standalone: true,
  imports: [
    ReactiveFormsModule,
    FormsModule,
    IonContent,
    IonButton,
    IonIcon,
    IonInput,
    IonItem,
    IonList,
    IonText,
    AppbarComponent,
    IonHeader,
    IonTitle,
    IonToolbar,
  ],
})
export class RegisterComponent {
  protected baseForm!: FormGroup;
  protected isLoading = signal<boolean>(false);

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private navCtrl: NavController,
    private toastService: ToastService,
  ) {
    trackEvent('register_page');
    this.initForm();
    addIcons({
      mail,
      person,
      call,
    });
  }

  private initForm() {
    this.baseForm = this.fb.group({
      email: [
        null,
        [Validators.required, Validators.pattern(CustomValidators.emailRegex)],
      ],
      name: [null, [Validators.required, Validators.minLength(2)]],
      surname: [null, [Validators.required, Validators.minLength(2)]],
      mobilePhone: [
        null,
        [
          Validators.required,
          Validators.minLength(10),
          Validators.maxLength(10),
        ],
      ],
    });
  }

  onBackClick() {
    this.navCtrl.navigateBack(['']);
  }

  onRegisterClick() {
    this.isLoading.set(true);
    this.authService
      .register({
        name: this.baseForm.value.name,
        surname: this.baseForm.value.surname,
        email: this.baseForm.value.email,
        mobilePhone: this.baseForm.value.mobilePhone.toString(),
      })
      .subscribe({
        next: () => {
          this.toastService.showSuccess('Registrazione avvenuta con successo!');
          this.isLoading.set(false);
          this.navCtrl.navigateForward(['auth/login']);
        },
        error: () => {
          this.isLoading.set(false);
        },
      });
  }

  onLoginClick() {
    this.navCtrl.navigateForward(['auth/login']);
  }
}
