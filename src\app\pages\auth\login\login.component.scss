ion-text {
  p {
    color: var(
      --ion-color-step-800,
      #333333
    ); /* Colore grigio scuro per il testo */
    font-size: 14px;
    line-height: 1.5;
    margin-top: 16px;
  }

  a {
    color: var(--ion-color-primary);
    font-weight: 500;
    cursor: pointer;
  }
}

ion-list {
  margin-bottom: 24px;
}

/* Elementi fissi in basso */
.fixed-bottom-elements {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  z-index: 1000;

  ion-button {
    margin-bottom: 16px;
  }

  ion-text {
    display: block;
    margin-bottom: 8px;

    p {
      margin: 0;
    }
  }
}

/* Spazio extra per evitare che il contenuto venga nascosto dagli elementi fissi */
.bottom-spacer {
  height: 120px; /* Altezza degli elementi fissi + padding */
}
