import { Component, signal } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/auth.service';
import {
  IonButton,
  IonContent,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonList,
  IonText,
  IonTitle,
  IonToolbar,
  NavController,
} from '@ionic/angular/standalone';
import { AppbarComponent } from '@shared/appbar/appbar.component';
import { addIcons } from 'ionicons';
import { lockClosed } from 'ionicons/icons';

@Component({
  selector: 'app-verify-otp',
  templateUrl: './verify-otp.component.html',
  styleUrls: ['./verify-otp.component.scss'],
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    IonButton,
    IonContent,
    IonList,
    IonItem,
    IonInput,
    IonIcon,
    IonText,
    AppbarComponent,
    IonHeader,
    IonTitle,
    IonToolbar,
  ],
})
export class VerifyOtpComponent {
  protected code = signal<string>('');
  protected isLoading = signal<boolean>(false);
  protected user = this.authService.user;

  constructor(
    private navCtrl: NavController,
    private authService: AuthService,
  ) {
    trackEvent('verify_otp_page');
    addIcons({ lockClosed });
  }

  onBackClick() {
    this.authService.setUser(undefined);
    this.navCtrl.navigateBack(['/login']);
  }

  onSubmitClick() {
    this.isLoading.set(true);
    this.authService
      .confirmOtp(this.user()?.email!, this.code()?.toString())
      .subscribe({
        next: (data) => {
          this.navCtrl.navigateForward(['/user']);
        },
        error: (err) => {
          this.isLoading.set(false);
        },
      });
  }

  onSendCodeAgain() {
    this.isLoading.set(true);
    this.authService.login(this.user()?.email!).subscribe({
      next: () => {
        this.isLoading.set(false);
      },
      error: () => {
        this.isLoading.set(false);
      },
    });
  }
}
