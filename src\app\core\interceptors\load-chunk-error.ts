import { ErrorHandler, Injectable, Injector } from '@angular/core';

@Injectable()
export class LoadChunkErrorHandler implements ErrorHandler {
  constructor(private readonly injector: Injector) {}

  handleError(error: any): void {
    const chunkFailedMessage = /Loading chunk [\d]+ failed/;
    if (chunkFailedMessage.test(error.message)) {
      console.error(error);
      this.reloadApp();
    }
  }

  reloadApp(): void {
    const initialState = {
      modalTitleText: 'NEW_VERSION_AVAILABLE_TITLE',
      modalBodyTextKey: 'NEW_VERSION_AVAILABLE_BODY'
    };
    window.location.reload();
  }
}
