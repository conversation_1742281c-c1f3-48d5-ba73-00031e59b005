.pt-0 {
  padding-top: 0;
}

.pb-0 {
  padding-bottom: 0;
}

.profile-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
  .profile-avatar {
    width: 64px;
    height: 64px;
  }
}

.profile-info {
  width: 100%;
  font-weight: bold;
  text-align: center;
}

ion-list {
  margin-top: 8px;
  margin-bottom: 24px;
}

.statistics-container {
  margin-top: 16px;
  ion-col {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .text {
      font-size: 14px;
    }

    .icon-container {
      display: flex;
      justify-content: center;

      .value {
        font-weight: 600;
        font-size: 24px;
        color: var(--ion-color-primary);
      }
    }
  }
}

/* Spazio extra per evitare che il contenuto venga nascosto dagli elementi fissi */
.bottom-spacer {
  height: 120px; /* Altezza degli elementi fissi + padding */
}

ion-card-content {
  padding-inline-end: 8px;
  padding-inline-start: 8px;
  padding: 8px;
}

ion-icon {
  color: var(--ion-color-medium);
}

ion-card {
  box-shadow: none;
}

.logout-button {
  margin-top: 32px;
  width: 100%;
  text-align: center;
  cursor: pointer;
  .danger {
    color: var(--ion-color-danger);
  }
}
