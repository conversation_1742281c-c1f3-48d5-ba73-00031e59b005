{"compileOnSave": false, "compilerOptions": {"outDir": "./dist/out-tsc", "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "esModuleInterop": true, "skipLibCheck": true, "noFallthroughCasesInSwitch": true, "resolveJsonModule": true, "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "types": ["jest", "node"], "moduleResolution": "node", "importHelpers": true, "target": "es2022", "module": "es2020", "lib": ["ES2022", "dom"], "useDefineForClassFields": false, "paths": {"@core/*": ["./src/app/core/*"], "@pages/*": ["./src/app/pages/*"], "@models/*": ["./src/app/models/*"], "@shared/*": ["./src/app/shared/*"], "@env/*": ["./src/environments/*"], "@app/*": ["./src/app/*"]}}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}