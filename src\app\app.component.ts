import { Component } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { init, trackEvent } from '@aptabase/web';
import { SettingsService } from '@core/services/settings.service';
import { IonApp, IonRouterOutlet } from '@ionic/angular/standalone';
import packageJson from '../../package.json';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
  standalone: true,
  imports: [IonApp, IonRouterOutlet],
})
export class AppComponent {
  protected company = this.settingsService.companyInfo;

  constructor(
    private settingsService: SettingsService,
    private title: Title,
    private meta: Meta,
  ) {
    // Aggiorna dinamicamente titolo e metadati all'avvio
    const company = this.company();
    const companyName = company?.name || 'Biuti';
    const description = `Prenota un appuntamento da ${companyName} in modo semplice e veloce.`;
    const url = window.location.href;
    const imageUrl = company?.images?.[0]?.url || 'assets/images/logo.png';
    this.title.setTitle(companyName);
    this.meta.updateTag({ name: 'description', content: description });
    this.meta.updateTag({ property: 'og:title', content: companyName });
    this.meta.updateTag({ property: 'og:description', content: description });
    this.meta.updateTag({ property: 'og:url', content: url });
    this.meta.updateTag({ property: 'og:image', content: imageUrl });
    this.meta.updateTag({ property: 'og:type', content: 'website' });
    this.meta.updateTag({ property: 'og:site_name', content: companyName });
    this.meta.updateTag({ property: 'og:locale', content: 'it_IT' });
    this.meta.updateTag({ property: 'og:image:type', content: 'image/png' });
    this.meta.updateTag({ property: 'og:image:width', content: '1200' });
    this.meta.updateTag({ property: 'og:image:height', content: '630' });
    this.meta.updateTag({ property: 'og:image:alt', content: companyName });
    this.meta.updateTag({ property: 'og:image:secure_url', content: imageUrl });

    init(process.env.APTABASE_API_KEY, {
      appVersion: packageJson.version,
      host: process.env.APTABASE_HOST,
      isDebug: process.env.APTABASE_IS_DEBUG === 'true',
    });
    trackEvent('app_loaded');
  }
}
