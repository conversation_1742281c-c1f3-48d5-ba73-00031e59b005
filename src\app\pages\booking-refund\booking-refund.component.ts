import { Component, inject, input, OnInit, signal } from '@angular/core';
import { Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { BookingService } from '@core/services/booking.service';
import { PaymentsService } from '@core/services/payment.service';
import { ToastService } from '@core/services/toast.service';
import {
  IonButton,
  IonContent,
  IonHeader,
  IonTitle,
  IonToolbar,
} from '@ionic/angular/standalone';
import { AppbarComponent } from '@shared/appbar/appbar.component';

@Component({
  selector: 'app-booking-refund',
  templateUrl: './booking-refund.component.html',
  styleUrls: ['./booking-refund.component.scss'],
  imports: [
    AppbarComponent,
    IonContent,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButton,
  ],
})
export class BookingRefundComponent implements OnInit {
  // Services
  private bookingService = inject(BookingService);
  private paymentsService = inject(PaymentsService);
  private toastService = inject(ToastService);
  private router = inject(Router);

  // Receive paymentId from URL path
  readonly paymentId = input<string>();

  protected currentBooking = this.bookingService.currentBooking;

  protected isLoading = signal<boolean>(false);

  constructor() {
    trackEvent('booking_refund_page');
  }

  ngOnInit() {
    // console.log('paymentId', this.paymentId());
  }

  refund() {
    console.log('refund');
    this.paymentsService.refundPayment(this.paymentId()!).subscribe({
      next: () => {
        this.toastService.showSuccess('Rimborso avviato con successo');
        this.router.navigate(['/']);
      },
      error: () => {
        this.toastService.showError(
          'Errore nel rimborso, contatta il supporto',
        );
      },
    });
  }
}
