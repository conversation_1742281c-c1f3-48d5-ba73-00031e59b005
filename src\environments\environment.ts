import { ISettings } from '@models/interfaces/configuration';

export const environment: ISettings = {
  production: `${process.env['PRODUCTION']}` === 'true',
  api: {
    users: `${process.env['BASE_API_URL']}/api/v1/users`,
    auth: `${process.env['BASE_API_URL']}/api/v1/auth/users`,
    bookings: `${process.env['BASE_API_URL']}/api/v1/bookings`,
    services: `${process.env['BASE_API_URL']}/api/v1/services`,
    staff: `${process.env['BASE_API_URL']}/api/v1/staff`,
    settings: `${process.env['BASE_API_URL']}/api/v1/settings`,
    payments: `${process.env['BASE_API_URL']}/api/v1/payments`,
  },
  stripeKey: `${process.env['STRIPE_PUBLISHABLE_KEY']}`,
};
/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
