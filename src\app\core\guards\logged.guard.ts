import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { AuthService } from '@core/services/auth.service';
import { NavController } from '@ionic/angular/standalone';

export const loggedGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const navCtrl = inject(NavController);

  const currentUrl = state.url;

  return authService.isLoggedIn() ? navCtrl.navigateForward(['/user']) : true;
};
