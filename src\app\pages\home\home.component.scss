.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 16px;
  text-align: center;
}

.container {
  --padding-top: 0;
  --padding-bottom: 80px; /* Spazio per il pulsante fisso */

  .sticky-header {
    position: sticky;
    top: 0;
    z-index: 100;
    transition: padding 0.2s ease;

    &::after {
      display: none;
    }

    ion-toolbar {
      padding: 0;

      .subtitle {
        margin: 0;
        margin-bottom: 24px;
        color: var(--ion-color-medium);
      }
    }
  }
  .custom-search-container {
    background: var(--ion-color-white);
    border-radius: 100px;
    display: flex;
    align-items: center;
    height: 50px;
    margin: 0 8px;
    padding: 16px;
    margin-bottom: 16px;

    .search-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      ion-icon {
        color: #636363;
      }
    }

    .custom-search-input {
      flex: 1;
      border: none;
      background: transparent;
      font-size: 18px;
      height: 100%;
      color: #000;

      &::placeholder {
        color: #636363;
        opacity: 0.7;
      }

      &:focus {
        outline: none;
      }
    }
  }

  .services-section {
    padding-top: 0;
    padding-bottom: 8px;
    padding-left: 8px;
    padding-right: 8px;
  }

  .services-container {
    margin-top: 8px;
    padding-bottom: 16px;
  }
}

/* Pulsante fisso in basso */
.fixed-bottom-button {
  .vat-number {
    text-align: center;
    font-size: 12px;
    color: var(--ion-color-medium);
  }
  .custom-button {
    position: relative;

    span {
      margin-right: 8px;
    }

    .counter-badge {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.3);
      color: white;
      font-size: 14px;
      font-weight: bold;
    }
  }
}

/* Spazio extra alla fine del contenuto per evitare che il pulsante fisso nasconda il contenuto */
.bottom-spacer {
  height: 60px;
}

@keyframes slide-up {
  0% {
    transform: translateY(100%);
  }

  100% {
    transform: translateY(0);
  }
}

.footer {
  margin-bottom: -12px;
  display: flex;
  align-items: center;
  justify-content: center;
  ion-icon {
    color: var(--ion-color-primary);
  }
}
