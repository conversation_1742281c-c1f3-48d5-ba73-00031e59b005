ion-text {
  p {
    color: var(--ion-color-step-800, #333333);
    font-size: 14px;
    line-height: 1.5;
    margin-top: 16px;
  }

  .info-text {
    color: var(--ion-color-medium);
    font-style: italic;
    margin-top: 8px;
    margin-bottom: 8px;
  }
}

form {
  margin-top: 16px;
}

// Stile per la sezione di pagamento
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-top: 20px;
  margin-bottom: 16px;

  &:not(:first-of-type) {
    margin-top: 40px;
  }
}

ion-radio {
  --color: #cccccc;
  --color-checked: var(--ion-color-primary);
  --border-radius: 50%;
  --border-width: 2px;
  --inner-border-radius: 50%;

  &::part(container) {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid #cccccc;
  }

  &.radio-checked::part(container) {
    border-color: var(--ion-color-primary);
  }

  &.radio-checked::part(mark) {
    width: 12px;
    height: 12px;
    background: var(--ion-color-primary);
    border-radius: 50%;
  }
}

// Spazio extra per evitare che il contenuto venga nascosto dal pulsante fisso
.bottom-spacer {
  height: 80px;
}

// Stile per il pulsante di conferma prenotazione
.booking-button {
  --background: var(--ion-color-primary);
  --background-activated: rgba(
    0,
    0,
    0,
    0.1
  ); /* Sfondo più scuro quando premuto */
  --background-activated-opacity: 1; /* Opacità piena per l'effetto */
  --background-hover: var(
    --ion-color-primary
  ); /* Mantiene il colore primario per hover */
  --border-radius: 8px;
  margin: 0;
  height: 50px;
  font-weight: 600;
}

// Stili per gli input e la sezione di pagamento
ion-list.input-list {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
  background-color: transparent;
}

ion-item.custom-item {
  &.payment-method-item {
    --background: white;
    border-radius: 12px;
    margin-bottom: 16px;
  }
}
