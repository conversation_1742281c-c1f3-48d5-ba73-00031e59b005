<app-appbar
  [title]="'Verifica codice'"
  [showBackButton]="true"
  [isLoading]="isLoading()"
  (backClick)="onBackClick()"
></app-appbar>

<ion-content>
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Verifica codice</ion-title>
      <p class="subtitle ion-padding-start ion-padding-end mb-0">
        Inserisci il codice di verifica che abbiamo inviato a
        <b>{{ user()?.email }}</b
        >.
      </p>
    </ion-toolbar>
  </ion-header>
  <form class="ion-padding">
    <ion-list class="input-list">
      <ion-item lines="none" class="custom-item">
        <ion-icon slot="start" name="lock-closed" aria-hidden="true"></ion-icon>
        <ion-input
          placeholder="Inserisci il codice OTP"
          maxlength="4"
          type="tel"
          minlength="4"
          [(ngModel)]="code"
          [disabled]="isLoading()"
          [ngModelOptions]="{ standalone: true }"
        ></ion-input>
      </ion-item>
    </ion-list>
  </form>

  <!-- Spazio extra per evitare che il contenuto venga nascosto dagli elementi fissi -->
  <div class="bottom-spacer"></div>
</ion-content>

<!-- Elementi fissi in basso -->
<div class="fixed-bottom-elements">
  <ion-button expand="block" (click)="onSubmitClick()" [disabled]="isLoading()"
    >Verifica</ion-button
  >

  <ion-text class="ion-text-center">
    <p>
      Non hai ricevuto il codice?
      <a (click)="onSendCodeAgain()">Invia nuovamente</a>
    </p>
  </ion-text>
</div>
