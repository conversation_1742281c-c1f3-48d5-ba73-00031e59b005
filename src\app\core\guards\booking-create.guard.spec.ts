import { TestBed } from '@angular/core/testing';
import { CanActivateFn } from '@angular/router';
import { bookingCreateGuard } from './booking-create.guard';

describe('bookingGuardGuard', () => {
  const executeGuard: CanActivateFn = (...guardParameters) =>
    TestBed.runInInjectionContext(() => bookingCreateGuard(...guardParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeGuard).toBeTruthy();
  });
});
