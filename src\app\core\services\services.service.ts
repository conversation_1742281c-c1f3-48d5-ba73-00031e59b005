import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
import { IBaseResponse } from '@models/interfaces/base-response';
import { IService } from '@models/interfaces/booking';
import { tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ServicesService {
  private _baseServiceApi = environment.api.services!;

  private _services = signal<IService[]>([]);
  public services = this._services.asReadonly();
  public selectedServices = signal<IService[]>([]);

  constructor(private http: HttpClient) {}

  setServices(services: IService[]) {
    this._services.set(services);
  }

  getServices() {
    return this.http.get<IBaseResponse<IService[]>>(this._baseServiceApi).pipe(
      tap((res) => {
        this.setServices(res.data!);
      }),
    );
  }

  manageSelectedServices(service: IService) {
    const serviceFound = this.selectedServices().find(
      (s) => s.id === service.id,
    );
    if (serviceFound) {
      this.selectedServices.update((prev) =>
        prev.filter((s) => s.id !== service.id),
      );
    } else {
      this.selectedServices.update((prev) => [...prev, service]);
    }
  }

  clearSelectedServices() {
    this.selectedServices.set([]);
  }
}
