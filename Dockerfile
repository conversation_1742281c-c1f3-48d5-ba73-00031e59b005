# Use the Node.js Alpine image as the base
FROM node:latest AS build

# Set the working directory
WORKDIR /usr/local/app

# Add the source code to app
COPY ./ /usr/local/app/

## Install dependencies
RUN npm install -g @angular/cli && npm install -g @ionic/cli && npm install @ionic/react && npm install @ionic/react-router && npm install

# Build the application, allowing an environment variable to be passed
RUN npm run build:prod

# Stage 2: Serve app with nginx server

# Use official nginx image as the base image
FROM nginx:latest

# Copy the custom NGINX configuration file
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy the build output to replace the default nginx contents
COPY --from=build /usr/local/app/public /usr/share/nginx/html

# Add debugging
RUN ls -la /usr/share/nginx/html

# Expose port 80
EXPOSE 80
