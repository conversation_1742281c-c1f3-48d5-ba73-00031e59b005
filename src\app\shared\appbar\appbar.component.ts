import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  output,
} from '@angular/core';
import {
  IonAvatar,
  IonBackButton,
  IonButton,
  IonButtons,
  IonChip,
  IonHeader,
  IonIcon,
  IonLabel,
  IonProgressBar,
  IonTitle,
  IonToolbar,
  NavController,
} from '@ionic/angular/standalone';
import { ICompany } from '@models/interfaces/company';
import { IUser } from '@models/interfaces/user';
import { addIcons } from 'ionicons';
import { arrowBackOutline } from 'ionicons/icons';

@Component({
  selector: 'app-appbar',
  imports: [
    IonIcon,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonProgressBar,
    IonHeader,
    IonBackButton,
    IonChip,
    IonAvatar,
    IonLabel,
  ],
  templateUrl: './appbar.component.html',
  styleUrls: ['./appbar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppbarComponent {
  // Inputs per la modalità standard (home, ecc.)
  company = input<ICompany | undefined>(undefined);
  rightIcon = input<string | undefined>(undefined);
  onRightIconClick = output<void>();
  navCtrl = inject(NavController);

  // Inputs per la modalità auth (login, register, ecc.)
  title = input<string | undefined>(undefined);
  showBackButton = input<boolean>(false);
  isLoading = input<boolean>(false);
  user = input<Partial<IUser> | undefined>(undefined);
  // Determina la modalità di visualizzazione
  mode = input<'home' | 'standard'>('standard');

  onBackButtonClick = output<void>();

  logoContainerHeight = 40; // default
  logoContainerWidth = 100; // default

  constructor() {
    addIcons({
      arrowBackOutline,
    });
  }

  onLogoLoad(event: Event) {
    const img = event.target as HTMLImageElement;
    const naturalWidth = img.naturalWidth;
    const naturalHeight = img.naturalHeight;
    const aspectRatio = naturalWidth / naturalHeight;

    if (aspectRatio > 0.9 && aspectRatio < 1.1) {
      // Se l'immagine è quasi quadrata, mantieni le dimensioni standard
      this.logoContainerWidth = 40;
      this.logoContainerHeight = 40;

      return;
    }

    // Regole custom per width/height in base alla larghezza reale
    // 201x81 => container 100x40
    // 400x167 => container 130x55
    if (naturalWidth <= 220) {
      this.logoContainerWidth = 90;
      this.logoContainerHeight = 40;
    } else {
      this.logoContainerWidth = 140;
      this.logoContainerHeight = 58;
    }
  }
}
