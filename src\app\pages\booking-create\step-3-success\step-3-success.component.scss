.success-container {
  display: flex;
  align-items: center;
  margin: 24px 0;
  justify-content: center;
}

.success-icon {

  ion-icon {
    margin-right: 4px;
    display: flex;
    font-size: 32px;
    color: #4caf50; // Verde per l'icona di conferma
  }
}

.success-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin: 0;
}

// Stili per i pulsanti fissi in basso
.button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  z-index: 100;
}

.calendar-button {
  margin-bottom: 12px;
  --background: white;
  --color: #333;
  --border-color: #ddd;
  --background-activated: rgba(
    0,
    0,
    0,
    0.1
  ); /* Sfondo più scuro quando premuto */
  --background-activated-opacity: 1; /* Opacità piena per l'effetto */
  --background-hover: #f5f5f5; /* Mantiene lo stesso colore per hover */
}

.done-button {
  --background: var(--ion-color-primary);
  --background-activated: rgba(
    0,
    0,
    0,
    0.1
  ); /* Sfondo più scuro quando premuto */
  --background-activated-opacity: 1; /* Opacità piena per l'effetto */
  --background-hover: var(
    --ion-color-primary
  ); /* Mantiene il colore primario per hover */
}

// Aggiunge spazio in fondo al contenuto per evitare che venga nascosto dai pulsanti fissi
ion-content::part(scroll) {
  padding-bottom: 140px;
}
