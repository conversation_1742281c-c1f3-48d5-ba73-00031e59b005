import { Component, input, signal } from '@angular/core';
import { trackEvent } from '@aptabase/web';
import { BookingService } from '@core/services/booking.service';
import {
  IonButton,
  IonContent,
  IonIcon,
  IonSpinner,
  NavController,
} from '@ionic/angular/standalone';
import { AppbarComponent } from '@shared/appbar/appbar.component';

@Component({
  selector: 'app-booking-delete',
  imports: [AppbarComponent, IonButton, IonIcon, IonSpinner, IonContent],
  templateUrl: './booking-delete.component.html',
  styleUrls: ['./booking-delete.component.scss'],
})
export class BookingDeleteComponent {
  // ID from URL path
  bookingId = input<string>();
  protected isLoading = signal<boolean>(true);
  protected bookingError = signal<boolean>(false);

  constructor(
    private bookingsService: BookingService,
    private navCtr: NavController,
  ) {
    trackEvent('booking_delete_page');
  }

  ionViewWillEnter() {
    this.bookingsService.deleteBooking(this.bookingId()!).subscribe({
      next: () => {
        this.isLoading.set(false);
      },
      error: () => {
        this.isLoading.set(false);
        this.bookingError.set(true);
      },
    });
  }
  onHomeClick() {
    this.navCtr.navigateRoot('/');
  }
}
