/* This sets a different item border color for the default theme on ios and md */
:root {
  --ion-item-border-color: var(--ion-background-color-step-200);
  --ion-font-family: "Instrument Sans", sans-serif;
  font-optical-sizing: auto;
  box-sizing: border-box;

  /* Colore primario e varianti */
  --ion-color-primary: #1e3d8e;
  --ion-color-primary-rgb: 26, 92, 255;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #1751e0;
  --ion-color-primary-tint: #316cff;

  /* Colore personalizzato per elementi selezionati */
  --ion-color-selected: #e0e7ff;
  --ion-color-selected-rgb: 224, 231, 255;
  --ion-color-selected-contrast: #1e3d8e;
  --ion-color-selected-contrast-rgb: 30, 61, 142;
  --ion-color-selected-shade: #c5cbe0;
  --ion-color-selected-tint: #e3eaff;
  --ion-color-white: #fff;
  --accordion-bg: #bad4ec;
  --medium-gray: #9ca3af;
  --strong-gray: #4b5563;
  --extra-light-gray: #f9fafb;
  --footer-bg: #070a10;
  --footer-subtitle: #5e6269;
  --stripe-color: #635bff;
  --stripe-color-rgb: 99, 91, 255;
}

/* This sets a different background and item background for the default theme on ios */
:root.ios {
  --ion-background-color: var(--ion-background-color-step-50, #f2f2f6);
  --ion-toolbar-background: var(--ion-background-color);
  --ion-item-background: #fff;
}

/* This sets a different background and item background for the default theme on md */
:root.md {
  --ion-background-color: var(--ion-background-color-step-100, #f9f9f9);
  --ion-toolbar-background: var(--ion-background-color);
  --ion-item-background: #fff;
}

/* This sets a different item background when dark mode is enabled on ios and md */
.ion-palette-dark.ios,
.ion-palette-dark.md {
  --ion-item-background: #1c1c1d;
}

.ion-palette-light.ios,
.ion-palette-light.md {
  --ion-item-background: #fff;
}

/* Classe per il colore personalizzato */
.ion-color-selected {
  --ion-color-base: var(--ion-color-selected);
  --ion-color-base-rgb: var(--ion-color-selected-rgb);
  --ion-color-contrast: var(--ion-color-selected-contrast);
  --ion-color-contrast-rgb: var(--ion-color-selected-contrast-rgb);
  --ion-color-shade: var(--ion-color-selected-shade);
  --ion-color-tint: var(--ion-color-selected-tint);
}
