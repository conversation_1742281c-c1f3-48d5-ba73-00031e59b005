<ion-content class="ion-padding">
  <div class="success-container">
    <div class="success-icon">
      <ion-icon name="checkmark-circle"></ion-icon>
    </div>
    <h1 class="success-title">Prenotazione Confermata!</h1>
  </div>

  <app-booking-summary
    [booking]="currentBooking()!"
    [showPane]="false"
  ></app-booking-summary>

  <div class="bottom-spacer"></div>

  <div class="button-container">
    <ion-button
      expand="block"
      class="calendar-button"
      (click)="addToCalendar()"
    >
      <ion-icon name="calendar-outline" slot="start"></ion-icon>
      Aggiungi al calendario
    </ion-button>

    <ion-button expand="block" class="done-button" (click)="goToHome()">
      Fatto
    </ion-button>
  </div>
</ion-content>
