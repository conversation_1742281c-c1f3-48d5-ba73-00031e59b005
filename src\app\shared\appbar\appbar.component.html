<!-- Modalità home -->
@if (mode() === "home" && company()) {
  <ion-header class="home-header">
    <ion-toolbar>
      <div
        class="logo-container"
        [style.width.px]="logoContainerWidth"
        [style.height.px]="logoContainerHeight"
      >
        <img
          class="logo"
          [alt]="company()!.name"
          [src]="company()!.images[0].url"
          style="width: 100%; height: 100%; object-fit: contain; display: block"
          (load)="onLogoLoad($event)"
        />
      </div>
      @if (rightIcon()) {
        <ion-chip
          slot="end"
          class="user-label-container"
          (click)="onRightIconClick.emit()"
        >
          <ion-label class="user-label">{{
            user()?.name ? "Ciao " + user()!.name : "Accedi"
          }}</ion-label>
          <ion-avatar style="width: 32px; height: 32px">
            <img
              alt="Silhouette of a person's head"
              src="https://ionicframework.com/docs/img/demos/avatar.svg"
            />
          </ion-avatar>
        </ion-chip>
      }
      @if (isLoading()) {
        <ion-progress-bar type="indeterminate"></ion-progress-bar>
      }
    </ion-toolbar>
  </ion-header>
}

@if (mode() === "standard") {
  <ion-header>
    <ion-toolbar>
      @if (showBackButton()) {
        <ion-buttons slot="start">
          <div class="back-button-container">
            <ion-back-button
              [defaultHref]="'/'"
              title="Indietro"
              [text]="''"
              icon="arrow-back-outline"
              (click)="onBackButtonClick.emit()"
            ></ion-back-button>
          </div>
        </ion-buttons>
      }
      @if (title()) {
        <ion-title>{{ title() }}</ion-title>
      }
      @if (rightIcon()) {
        <ion-buttons slot="end">
          <ion-button (click)="onRightIconClick.emit()">
            <ion-icon slot="icon-only" [name]="rightIcon()"></ion-icon>
          </ion-button>
        </ion-buttons>
      }
      @if (isLoading()) {
        <ion-progress-bar type="indeterminate"></ion-progress-bar>
      }
    </ion-toolbar>
  </ion-header>
}
