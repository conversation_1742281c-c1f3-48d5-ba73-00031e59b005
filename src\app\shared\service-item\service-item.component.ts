import { CurrencyPipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  input,
  output,
} from '@angular/core';
import {
  IonAvatar,
  IonCheckbox,
  IonItem,
  IonLabel,
  IonNote,
} from '@ionic/angular/standalone';
import { IService } from '@models/interfaces/booking';

@Component({
  selector: 'app-service-item',
  templateUrl: './service-item.component.html',
  styleUrls: ['./service-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IonItem, IonCheckbox, IonLabel, IonNote, IonAvatar, CurrencyPipe],
})
export class ServiceItemComponent {
  onItemSelected = output<IService>();
  service = input.required<IService>();
  isSelected = input<boolean>(false);

  constructor() {}

  onClick(service: IService) {
    this.onItemSelected.emit(service);
  }
}
